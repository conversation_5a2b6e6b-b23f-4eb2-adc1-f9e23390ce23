import { getToken, getUserInfo } from "@/utils/auth";

interface DocumentDraftingParams {
  [key: string]: any;
}

interface DocumentDraftingCallbacks {
  onMessage?: (message: string) => void;
  onError?: (error: any) => void;
  onFinish?: (data?: any) => void;
}

interface SSEChat {
  start: (config: any) => void;
}

const appKey = "8d152de0-ae25-438f-9d64-66e2350dba3a";

export async function documentDrafting(
  inputs: DocumentDraftingParams,
  callbacks: DocumentDraftingCallbacks,
  sseChat: SSEChat
) {
  const tokenInfo = await getToken();
  const userInfo = await getUserInfo();
  
  return sseChat.start({
    url: "/dify/broker/agent/stream",
    headers: {
      "Content-Type": "application/json",
      Token: tokenInfo || "",
    },
    body: {
      insId: "1",
      bizType: "app:agent",
      bizId: appKey,
      agentId: appKey,
      path: "/chat-messages",
      difyJson: {
        inputs: {
          appKey,
          ...inputs
        },
        response_mode: "streaming",
        user: userInfo?.id || "document-drafting-user",
        conversation_id: "",
        query: inputs.query || "1",
      },
    },
    query: {},
    onMessage: callbacks.onMessage,
    onFinished: callbacks.onFinish,
  });
}

