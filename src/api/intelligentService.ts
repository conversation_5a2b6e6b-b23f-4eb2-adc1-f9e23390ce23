import { SSERequest, SSEClient } from "@/utils/sse-client";
import request from "@/utils/request";
const appKey =
  import.meta.env["VITE_DOCUMENT_SERVICE_TOKEN"] ||
  "app-xpGpkTHLNc2eDZLlddS0irhJ";

export async function intelligentService(
  { query, ...inputs }: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, "onMessage" | "onError" | "onFinish">
) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: query || "1",
    user: "document-drafting-user",
    inputs: {
      appKey,
      ...inputs,
    },
    onMessage,
    onError,
    onFinish,
  });
}
