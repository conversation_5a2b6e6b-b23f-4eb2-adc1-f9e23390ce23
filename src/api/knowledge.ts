import request from "@/utils/request";

const VITE_API_BASE_DOC: string = import.meta.env["VITE_API_BASE_DOC"] || "";
export function getItemDetail(query: any): Promise<any> {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/kn/page`,
    method: "POST",
    data: query,
  });
}

// 查询最近的知识库
export function recentList(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/lib/recent/list`,
    method: "POST",
    data,
  });
}

// 知识/知识库整体搜索
export function baseGetList(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/lib/getList`,
    method: "POST",
    data,
  });
}
// 知识预览
export function onlinePreviewUrl(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/kn/onlinePreview?id=${data}`,
    method: "GET",
  });
}

// 新增个人库
export function addKnowledge(query: any): Promise<any> {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/lib/individual/add`,
    method: "POST",
    data: query,
  });
}

// 存入个人库
export function addNoteKnowledge(query: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/kn/note/add`,
    method: "POST",
    data: query,
  });
}

// 获取个人绑定了本地路径的知识库列表
export function bindedList(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/lib/individual/bindedList`,
    method: "POST",
    data,
  });
}

// 根据知识库 id获取绑定信息
export function getBindInfoByLibId(query: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/lib/individual/getBindInfoByLibId`,
    method: "GET",
    params: query,
  });
}
