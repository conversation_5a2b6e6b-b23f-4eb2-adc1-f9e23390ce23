import request from "@/utils/request";
import { base64ToFile } from "@/utils/common";
import { getToken } from "@/utils/auth";
import { cacheGet } from "@/utils/cacheUtil";
const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";
const API_BASE_DOC: string = import.meta.env["VITE_API_BASE_DOC"] || ""; 
const API_BASE_SPLIT = import.meta.env["VITE_API_BASE_SPLIT"] || "";
const API_BASE_NOTE = import.meta.env["VITE_API_BASE_NOTE"] || "";
const API_BASE_PUB = import.meta.env["VITE_API_BASE_PUB"] || "";

// 上传文件
export async function uploadChatFile(params: any) {
  const formData = new FormData();
  if (params?.remoteFileUrl) {
    // 写作模板需要上传dify但是没有文件信息
    for (const key in params) {
      formData.append(key, params[key]);
    }
  } else {
    const file = base64ToFile(params.fileStr, params.fileName);
    formData.append("file", file); // 将文件附加到 FormData
    // 将params的其他属性也放到formData中
    for (const key in params) {
      if (key !== "fileStr" && key !== "fileName") {
        formData.append(key, params[key]);
      }
    }
  }
  return request({
    url: `${API_BASE_INS}/dify/broker/formData`,
    method: "POST",
    headers: {},
    data: formData,
  });
}

// 模版填充工具
export async function convertFileToPDF(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  const tokenInfo = getToken();
  const tenantId = cacheGet("tenantId");
  return fetch(`${API_BASE_DOC}/doc/resume/formatConvert`, {
    method: "POST",
    headers: {
      Tenantid: tenantId || "",
      Token: tokenInfo || "",
    },
    body: formData,
  });
}

// 定义切割模式枚举
export enum SplitModel {
  NO_SPLIT = "file_split_model_no_split",
  BY_PAGE = "file_split_model_split_by_page",
  BY_SHEET = "file_split_model_split_by_sheet",
  BY_TITLE = "file_split_model_split_by_title",
  BY_SIZE = "file_split_model_split_by_size",
}

// 定义切割状态枚举
export enum SplitStat {
  NO_SPLIT = "split_stat_no_split",
  SPLITTING = "split_stat_split_ing",
  SUCCESS = "split_stat_split_success",
  FAIL = "split_stat_split_fail",
}

export enum SplitParam {
  LEVEL_1 = "title_level_01",
  LEVEL_2 = "title_level_02",
  LEVEL_3 = "title_level_03",
}

export interface CommonChunk {
  id: string;
  content: string;
  metadata: any;
}

// 定义切割响应接口
export interface CommonSplitResponse {
  splitState: SplitStat;
  chunkInfo: CommonChunk[];
}

export async function splitFile(file: File, titleLevel: number = 2) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("titleLevel", titleLevel.toString());

  const res = await fetch(`${API_BASE_SPLIT}/common-split-word`, {
    method: "POST",
    body: formData,
  });
  if (!res) {
    throw new Error(`HTTP error! status: ${res}`);
  }
  console.log(res, 342);
  // 这里才是解析返回的 JSON
  const data = await res.json();
  return data;
}

/** 查询人员列表 */
export function listNoteRela(params: any): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/noteObjRela/relaList`,
    method: "GET",
    params,
  });
}

// 协同
/**
 * 提交场景并获取通知列表
 * @param params - 请求参数对象，包含查询所需的各种条件
 * @returns - 返回一个Promise对象，包含请求结果
 */
export function scenarioSubmit(query: any) {
  return request({
    // 请求的URL地址
    url: `${API_BASE_PUB}/pub/scenario/submit`,
    method: "POST",
    data: query,
  });
}

// 获取协同场景详情
export function scenarioDetail(params: any) {
  return request({
    // 请求的URL地址
    url: `${API_BASE_PUB}/pub/scenario/detail`,
    method: "GET",
    params,
  });
}

// 通用知识文件上传
export function uploadCommonFile(file: File) {
  const formData = new FormData()
  formData.append("file", file)
  return request({
    url: `${API_BASE_DOC}/knowledge/file/common/upload`,
    method: "POST",
    data: formData,
  });
}