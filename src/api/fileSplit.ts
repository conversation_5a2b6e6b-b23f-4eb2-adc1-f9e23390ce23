import {
  IntelligentStorage,
  KnowledgeListPar,
  KnowledgeListRes,
  NotSplitList,
  Result,
  SyncSplit,
} from "@/types/fileSplit";
import request from "@/utils/request";
const API_BASE_DOC: string = import.meta.env["VITE_API_BASE_DOC"] || "";

// 知识库列表
export function getKnowledgeList(query: KnowledgeListPar): Promise<any> {
  return request<Result<KnowledgeListRes>>({
    url: `${API_BASE_DOC}/knowledge/lib/individual/list`,
    method: "POST",
    data: query,
  });
}

// 知识库详情
export function getKnowledgeDetail(id: string): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/lib/sub/info?id=${id}`,
    method: "GET",
  });
}

// 智能入库自动选择
export function intelligentStorage(fileIds: IntelligentStorage): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/kn/intelligent/getLib`,
    method: "POST",
    data: fileIds,
  });
}

// 文件预览
export function previewFile(fileId: string): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/file/onlinePreview?id=${fileId}`,
    method: "GET",
  });
}

// 知识新增不拆分
export function addNotSplit(data: NotSplitList): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/kn/addNotSplit`,
    method: "POST",
    data,
  });
}

//同步拆分接口
export function syncSplit(data: SyncSplit): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/kn/syncSplit`,
    method: "POST",
    data,
  });
}

//知识新增（包含拆分段落）
export function addConSplitPar(data: SyncSplit): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/kn/addConSplitPar`,
    method: "POST",
    data,
  });
}
