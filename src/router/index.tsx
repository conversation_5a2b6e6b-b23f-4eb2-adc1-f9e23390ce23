// router.tsx
import React, { lazy, Suspense } from "react";
import { create<PERSON><PERSON>erRouter, RouterProvider } from "react-router-dom";
import { Spin } from "antd";
import { App } from "@/App";

const LazyLogin = lazy(() => import("@/pages/login"));
const LazyTool = lazy(() => import("@/pages/tool"));
const LazyTest = lazy(() => import("@/pages/test"));
const TestIframe = lazy(() => import("@/pages/testIframe"));
const LazyInterview = lazy(() => import("@/pages/intelligent-interview")); // 确保路径没错
const LazyNotFound = lazy(() => import("@/pages/not-found")); // 确保路径没错
const LegalReview = lazy(() => import("@/pages/legalReview")); // 合同法审
const FileSplit = lazy(() => import("@/pages/fileSplit")); // 智能文件拆分
const IntelligentService = lazy(() => import("@/pages/intelligentService")); // 智能客服
const SetQuestions = lazy(() => import("@/pages/setQuestions")); // 智能出题
const LocalFile = lazy(() => import("@/pages/localFile")); // 本地文件助手
const WebImg = lazy(() => import("@/pages/webImg")); // 网页图片
const MultipleApproaches = lazy(() => import("@/pages/multipleApproaches")); // 多思路与解析
const QuestionVerification = lazy(() => import("@/pages/questionVerification")); // 智能考题核验
const IntelligentCustomer = lazy(() => import("@/pages/intelligentCustomer")); // 智能客户
// const IntelligentService
const LazyDocumentDrafting = lazy(() => import("@/pages/document-drafting")); // 文书起草助手

function Loading() {
  return (
    <div
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Spin size="large" />
    </div>
  );
}

export const routes = [
  {
    path: "/",
    element: <App />, // layout
    children: [
      {
        path: "/login",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyLogin />
          </Suspense>
        ),
      },
      {
        path: "tool",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyTool />
          </Suspense>
        ),
      },
      {
        path: "test",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyTest />
          </Suspense>
        ),
      },
      {
        path: "testIframe",
        element: (
          <Suspense fallback={<Loading />}>
            <TestIframe />
          </Suspense>
        ),
      },
      {
        path: "setQuestions",
        element: (
          <Suspense fallback={<Loading />}>
            <SetQuestions />
          </Suspense>
        ),
      },
      {
        path: "multipleApproaches",
        element: (
          <Suspense fallback={<Loading />}>
            <MultipleApproaches />
          </Suspense>
        ),
      },
      {
        path: "legalReview",
        element: (
          <Suspense fallback={<Loading />}>
            <LegalReview />
          </Suspense>
        ),
      },
      {
        path: "intelligent-interview",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyInterview />
          </Suspense>
        ),
      },
      {
        path: "file-split",
        element: (
          <Suspense fallback={<Loading />}>
            <FileSplit />
          </Suspense>
        ),
      },
      {
        path: "localFile",
        element: (
          <Suspense fallback={<Loading />}>
            <LocalFile />
          </Suspense>
        ),
      },
      {
        path: "webImg",
        element: (
          <Suspense fallback={<Loading />}>
            <WebImg />
          </Suspense>
        ),
      },
      {
        path: "intelligent-service",
        element: (
          <Suspense fallback={<Loading />}>
            <IntelligentService />
          </Suspense>
        ),
      },
      {
        path: "questionVerification",
        element: (
          <Suspense fallback={<Loading />}>
            <QuestionVerification />
          </Suspense>
        ),
      },
      {
        path: "intelligent-customer",
        element: (
          <Suspense fallback={<Loading />}>
            <IntelligentCustomer />
          </Suspense>
        ),
      },
      {
        path: "document-drafting",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyDocumentDrafting />
          </Suspense>
        ),
      },
      {
        path: "*",
        element: (
          <Suspense fallback={<Loading />}>
            <LazyNotFound />
          </Suspense>
        ),
      },
    ],
  },
];

const router = createBrowserRouter(routes, { basename: "/copilot" });

export function Router() {
  return <RouterProvider router={router} />;
}
