export type KnowledgeInfo = {
  tags: string;
  libName: string;
  knInfoList: any[];
  libDesc: string;
};

export type Result<T> = {
  code: number;
  data: T;
  msg: string;
};

// 知识库列表
export type KnowledgeListPar = {
  libName: string;
};
export type KnowledgeListRes = KnowledgeListResItem[];
export type KnowledgeListResItem = {
  libName: string;
};

// 智能入库
export type IntelligentStorage = string[];

// 文件列表字典
export type UploadedFiles = UploadedFilesItem[];
export type UploadedFilesItem = {
  fileId: string;
  name: string;
  uid: string;
  uploaded: boolean;
  url?: string;
  libId: string;
  title: string;
};

// 知识新增不拆分
export type NotSplitList = NotSplitListItem[];
export type NotSplitListItem = {
  fileId: string;
  libId: string;
};

//同步拆分
export type SyncSplit = SyncSplitItem[];
export type SyncSplitItem = {
  fileId: string;
  libId: string;
  title: string;
};
export type SyncSplitListRes = SyncSplitListResItem[];
export type SyncSplitListResItem = {
  fileId: string;
  libId: string;
  title: string;
  chunkOverLapLength: number;
  chunkMaxLength: number;
  chunkList: ChunkList[];
  chunkSize: number;
  chunkSegmentString: string;
  splitType: string;
};
export type ChunkList = {
  chunkText: string;
  chunkId: string;
};
