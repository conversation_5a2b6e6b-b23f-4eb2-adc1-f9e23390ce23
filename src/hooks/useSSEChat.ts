import { useCallback, useEffect, useRef, useState } from "react";
import type { IFetchSSEArguments } from "@/types/message";
import { stopMessage } from "@/api/chat";
import { useGetState } from "ahooks";
import { cacheGet } from "@/utils/cacheUtil";
import useEventSource from "@/hooks/useEventSource";

export enum GenerationProgress {
  INITIALIZED,
  WAITING,
  RESPONDING,
  RESPONDED,
  RENDER_FINISHED,
  USER_CANCELED = 5,
  ERROR = 7,
}

export interface ChatMessage {
  id: string;
  role: "system" | "user" | "assistant";
  content: string;
  message_files?: [];
}

const MIN_UPDATE_INTERVAL = 16;
const CHARS_PER_UPDATE = 5;

interface SSEInstance {
  eventSource: any;
  responseText: string;
  textBuffer: string;
  updateInterval: NodeJS.Timeout | null;
  isRendering: boolean;
  images?: any;
}

function useSSEChat() {
  const chatListRef = useRef<Array<Array<ChatMessage>>>([]);
  const [, setChatList, getChatList] = useGetState<Array<Array<ChatMessage>>>(
    []
  );
  const [, setCurrentConversation, getCurrentConversation] =
    useGetState<string>("");
  const [, setTaskId, getTaskId] = useGetState<string>("");
  const [instances, setInstances] = useState<Record<string, SSEInstance>>({});

  const clearTimer = useCallback(
    (id: string) => {
      if (instances[id]?.updateInterval) {
        clearInterval(instances[id].updateInterval!);
        instances[id].updateInterval = null;
      }
    },
    [instances]
  );

  const updateDisplayedText = useCallback(
    (id: string, setDisplayedText: (text: string) => void) => {
      const instance = instances[id];
      if (!instance) return;
      const now = Date.now();

      if (
        (instance.responseText === "" && instance.textBuffer.length === 0) ||
        instance.isRendering
      ) {
        setDisplayedText(instance.responseText);
        clearTimer(id);
        instance.responseText = "";
        instance.isRendering = true;
      } else if (instance.textBuffer.length > 0) {
        const charsToAdd = instance.textBuffer.slice(0, CHARS_PER_UPDATE);
        setDisplayedText((prev) => prev + charsToAdd);
        instance.textBuffer = instance.textBuffer.slice(CHARS_PER_UPDATE);
      } else {
        setDisplayedText(instance.responseText);
      }
    },
    [instances, clearTimer]
  );

  const reset = useCallback(async () => {
    Object.keys(instances).forEach((id) => clearTimer(id));
    chatListRef.current = [];
    setChatList([]);
    setCurrentConversation("");
    setTaskId("");
    setInstances({});
  }, [clearTimer, setChatList, setCurrentConversation, setTaskId, instances]);

  const start = useCallback(
    ({
      message,
      url,
      headers,
      query,
      body,
      onMessage,
      onFinished,
    }: {
      message?: string;
      onMessage?: (text: string) => void;
      onFinished?: (text: string) => void;
    } & IFetchSSEArguments) => {
      const responseId = `assistant-${Date.now()}`;
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        role: "user",
        content: message || "",
      };
      const responseMessage: ChatMessage = {
        id: responseId,
        role: "assistant",
        content: "",
        message_files: [],
      };

      chatListRef.current.push([userMessage, responseMessage]);
      setChatList(chatListRef.current);
      setCurrentConversation(query.conversation_id || "");

      const instance: SSEInstance = {
        eventSource: null,
        responseText: "",
        textBuffer: "",
        updateInterval: null,
        isRendering: false,
        images: new Set<string>(),
      };
      setInstances((prev) => ({ ...prev, [responseId]: instance }));

      const allUrl = window.electronAPI
        ? body.insId
          ? `${import.meta.env.VITE_BASE_API}${
              import.meta.env.VITE_API_BASE_INS
            }${url}`
          : `${import.meta.env.VITE_BASE_AI_API}${url}`
        : body.insId
        ? `${import.meta.env.VITE_API_BASE_INS}${url}`
        : `/dify${url}`;

      instance.eventSource = useEventSource({
        url: allUrl,
        method: "POST",
        headers: {
          ...headers,
          Token: cacheGet("token") || "",
          TenantId: cacheGet("tenantId") || "",
        },
        body: {
          ...body,
          difyJson: {
            ...body.difyJson,
            conversation_id: query.conversation_id || "",
          },
        },
        onMessage: (msg) => {
          // 工具函数：把 Markdown 中的相对图片路径加上前缀
          const addBaseUrlToImages = (text: string) => {
            const base = import.meta.env.VITE_BASE_API;
            return text.replace(
              /!\[([^\]]*)\]\((\/[^\)]+)\)/g,
              (_, alt, url) => {
                const fullUrl = url.startsWith("http") ? url : `${base}${url}`;
                return `![${alt}](${fullUrl})`;
              }
            );
          };

          // 处理 msg.answer
          if (msg.answer) {
            const processedAnswer = addBaseUrlToImages(msg.answer);

            instance.responseText += processedAnswer;
            instance.textBuffer += processedAnswer;
          }

          // 定时更新页面显示
          if (!instance.updateInterval)
            instance.updateInterval = setInterval(
              () =>
                requestAnimationFrame(() =>
                  updateDisplayedText(responseId, instance.responseText)
                ),
              MIN_UPDATE_INTERVAL
            );

          // 回调通知外部
          Promise.resolve()
            .then()
            .then(() => onMessage?.(instance.responseText));
        },

        // onMessage: (msg) => {
        //   instance.responseText += msg.answer;
        //   instance.textBuffer += msg.answer;
        //   if (!instance.updateInterval)
        //     instance.updateInterval = setInterval(
        //       () =>
        //         requestAnimationFrame(() =>
        //           updateDisplayedText(responseId, (text) => {
        //             responseMessage.content = text;
        //             setChatList([...chatListRef.current]);
        //             onMessage?.(text);
        //           })
        //         ),
        //       MIN_UPDATE_INTERVAL
        //     );
        // },
        onMessageFile: (msg) => {
          console.log(msg, 122);
          if (msg.url) {
            // 拼完整路径
            const fullUrl = msg.url.startsWith("http")
              ? msg.url
              : `${import.meta.env.VITE_BASE_API}${msg.url}`;

            // 确保没有重复的图片
            if (!instance.images.has(fullUrl)) {
              instance.responseText += `![图片](${fullUrl})\n\n`;
              instance.images.add(fullUrl); // ✅ 记录完整路径，避免重复
            }
          }

          if (msg.answer) {
            instance.textBuffer += msg.answer;
            instance.responseText += msg.answer;
          }
        },
        onMessageEnd: (data) => {
          console.log(data, 333);
          responseMessage.content = instance.responseText;
          setChatList([...chatListRef.current]);
        },
        onFinished: () => {
          console.log(instance.responseText, 4444);
          clearTimer(responseId);
          instance.isRendering = true;
          responseMessage.content = instance.responseText;
          setChatList([...chatListRef.current]);
          onFinished?.(instance.responseText);
        },
        onClose: () => {
          clearTimer(responseId);
          instance.isRendering = true;
          responseMessage.content = instance.responseText || "已取消加载";
          setChatList([...chatListRef.current]);
        },
        onError: (msg) => {
          clearTimer(responseId);
          instance.isRendering = true;
          responseMessage.content = msg.msg || "发生错误";
          setChatList([...chatListRef.current]);
          onFinished?.(instance.responseText);
        },
      });

      instance.eventSource.start(query);

      return responseId;
    },
    [updateDisplayedText, setChatList]
  );

  const stop = useCallback(
    (id: string) => {
      const instance = instances[id];
      if (!instance) return;
      instance.eventSource?.stop();
      clearTimer(id);
      instance.isRendering = true;
    },
    [instances, clearTimer]
  );

  return {
    chatList: getChatList(),
    currentConversation: getCurrentConversation(),
    instances,
    start,
    stop,
    reset,
  };
}

export default useSSEChat;
