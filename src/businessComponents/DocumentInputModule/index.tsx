// 文书输入与文件上传组件
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import type { UploadProps } from "antd";
import "./index.less";
import {
  Button,
  Divider,
  Flex,
  Input,
  message,
  Spin,
  theme,
  Upload,
} from "antd";
import { getToken, getUserInfo } from "@/utils/auth";
import classNames from "classnames";
import { convertFileToPDF } from "@/api/public";
import {
  CloseCircleFilled,
  FolderAddOutlined,
} from "@ant-design/icons";

const { useToken } = theme;
const { TextArea } = Input;

interface DocumentInputProps {
  onQueryChange?: (query: string) => void;
}

export interface DocumentInputRef {
  getInputData: () => object;
  setInputData: (data: any) => void;
}

// 文件类型定义
type FileType = 'document' | 'image';

// 扩展RcFile类型，添加fileType属性
interface CustomFile extends File {
  fileType?: FileType;
  uid?: string;
}

// 定义文件数据类型
interface FileData {
  fileName: string;
  libName: string;
  libDesc: string;
  flag: string;
  fileType: FileType;
  url?: string;
  loading?: boolean;
  [key: string]: any;
}



const DocumentInputModule = forwardRef<DocumentInputRef, DocumentInputProps>(
  ({ onQueryChange }, ref) => {
    const { token: csstoken } = useToken();
    const [documentFiles, setDocumentFiles] = useState<FileData[]>([]);
    const [imageFiles, setImageFiles] = useState<FileData[]>([]);
    const [allFiles, setAllFiles] = useState<FileData[]>([]);
    const [query, setQuery] = useState<string>("");
    const [token, setToken] = useState<string>("");
    // 移除未使用的 localFile 状态
    const [, setLocalFile] = useState<FileData[]>([]);

    // 初始化数据
    useEffect(() => {
      const fetchData = async () => {
        const tokenInfo = await getToken();
        setToken(tokenInfo || "");
      };
      fetchData();
    }, []);

    const uploadProps: UploadProps = {
      name: "file",
      multiple: true, // 允许多文件上传
      headers: {
        [import.meta.env.VITE_API_HEADER_KEY]: token,
      },
      showUploadList: false,
      beforeUpload(file) {
        const isLt15M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
        if (!isLt15M) {
          message.error("不允许超过15MB!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
        
        // 允许上传的文件格式
        const fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf", "jpg", "jpeg", "png", "gif"];
        const arr = file.name.split(".");
        const fileExt = arr[arr.length - 1].toLowerCase() || "";
        
        if (!fileFormat.includes(fileExt)) {
          message.error("文件格式不正确!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
        
        // 判断文件类型
        const isImage = ["jpg", "jpeg", "png", "gif"].includes(fileExt);
        
        // 将文件类型信息添加到文件对象中，方便后续处理
        (file as CustomFile).fileType = isImage ? 'image' : 'document';
        return true;
      },
    };

    // 文件转base64
    function fileToBase64(file: File) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        // 成功读取文件时的回调
        reader.onload = () => {
          resolve(reader.result); // Base64 编码的字符串
        };

        // 读取文件失败时的回调
        reader.onerror = (error) => {
          reject(error);
        };

        // 读取文件并转为 Base64
        reader.readAsDataURL(file);
      });
    }

    const uploadQueue: CustomFile[] = []; // 存储待上传的文件
    let isUploading = false; // 标记是否正在上传

    const processQueue = async () => {
      if (uploadQueue.length === 0) {
        isUploading = false; // 队列为空时，停止上传状态
        return;
      }

      isUploading = true;
      const file = uploadQueue.shift()!;
      await uploadFileNew(file);

      // 处理完一个文件后，继续处理队列中的下一个文件
      isUploading = false;
      processQueue();
    };

    const handleCustomRequest = async (options: any) => {
      const { file } = options;
      
      // 添加新文件到队列
      uploadQueue.push(file);

      if (!isUploading) {
        processQueue();
      }
    };

    // 上传文件
    let queue: Promise<void> = Promise.resolve();

    // 使用Promise封装文件上传功能
    const uploadFileToServer = (file: File): Promise<any> => {
      return new Promise((resolve, reject) => {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('user', 'abc-123'); // 固定用户值

        // 获取API密钥
        const api_key = "app-KikE6u21RjjmqHrJ7gJoiYkt";

        // 发送POST请求
        fetch('https://copilot.sino-bridge.com:90/v1/files/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${api_key}`,
            // 不需要手动设置Content-Type，fetch会自动设置为multipart/form-data并添加boundary
          },
          body: formData
        })
        .then(response => {
          // 检查响应状态
          if (!response.ok) {
            throw new Error(`上传失败: ${response.status} ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          // 成功处理
          resolve(data);
        })
        .catch(error => {
          // 错误处理
          console.error('文件上传错误:', error);
          reject(error);
        });
      });
    };
    


    const uploadFileNew = (file: any) => {
      queue = queue.then(() => {
        return new Promise<void>((resolve, reject) => {
          let fileData: any = {
            fileName: "",
            fileStr: "",
          };
          if (!file) {
            resolve();
            return;
          }

          (async () => {
            const userInfo = await getUserInfo();
            try {
              fileData = {
                fileName: file.name,
                fileStr: await fileToBase64(file),
                loading: true,
                path: "/files/upload",
                user: userInfo?.id,
              };

              fileData.libName = file.name;
              const arr = file.name.split(".");
              fileData.libDesc = arr[arr.length - 1].toLowerCase();
              fileData.flag = "file";
              fileData.fileType = file.fileType || 'document';
              
              // 根据文件类型分别存储
              if (fileData.fileType === 'image') {
                setImageFiles((prev: FileData[]) => [...prev, fileData]);
              } else {
                setDocumentFiles((prev: FileData[]) => [...prev, fileData]);
              }
            } catch (error: any) {
              console.error("文件转 Base64 出错：", error);
              reject();
              return;
            }
            
            // 处理Word文档转PDF
    if (["docx", "doc"].includes(fileData.libDesc)) {
      convertFileToPDF(file).then(async (response) => {
        if (response["status"] && response["status"] !== 200) {
          message.open({
            key: "uploading",
            type: "error",
            content: "文件处理异常，请稍后重试",
            duration: 1,
          });
          // 上传失败时，从文档文件列表中移除
          setDocumentFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== fileData.fileName));
          reject(new Error("文件处理异常"));
        } else if ("blob" in response) {
          const blob = await response.blob();
          // 保持原始文件名，只改变扩展名
          const originalName = fileData.fileName.replace(/\.[^/.]+$/, ""); // 去除原扩展名
          const pdfFile = new File([blob], `${originalName}.pdf`, {
            type: "application/pdf",
          });
          
          // 使用新的uploadFileToServer方法上传PDF文件
          try {
            const result = await uploadFileToServer(pdfFile);
            if (result && result.name) {
              const res = result as any;
              res.fileName = `${originalName}.pdf`; // 使用原始文件名
              res.libName = `${originalName}.pdf`; // 显示名称也使用原始文件名
              res.libDesc = "pdf";
              res.originalFileType = fileData.libDesc; // 保存原始文件类型用于图标显示
              res.flag = "file";
              res.fileType = "document";
              res.url = URL.createObjectURL(pdfFile);
              
              // 更新文档文件列表
              const updatedFile: FileData = {
                fileName: res.fileName || `${originalName}.pdf`,
                libName: res.libName || `${originalName}.pdf`,
                libDesc: res.libDesc || "pdf",
                originalFileType: res.originalFileType || fileData.libDesc,
                flag: res.flag || "file",
                fileType: res.fileType || "document",
                url: res.url,
                loading: false,
                ...res
              };
              setDocumentFiles((prev: FileData[]) => {
                const updatedFiles = prev.filter((f: FileData) => f.fileName !== fileData.fileName);
                updatedFiles.push(updatedFile);
                return updatedFiles;
              });
              
              // 更新所有文件列表
              setAllFiles((prev: FileData[]) => {
                const updatedFiles = prev.filter((f: FileData) => f.fileName !== fileData.fileName);
                updatedFiles.push(updatedFile);
                return updatedFiles;
              });
              
              message.success("文件上传成功");
              resolve();
            } else {
              message.open({
                type: "error",
                content: "上传失败",
              });
              // 上传失败时，从文档文件列表中移除
              setDocumentFiles(prev => prev.filter(f => f.fileName !== fileData.fileName));
              reject(new Error("上传失败"));
            }
          } catch (error: unknown) {
            message.open({
              type: "error",
              content: "上传失败: " + (error instanceof Error ? error.message : "未知错误"),
            });
            // 上传失败时，从文档文件列表中移除
            setDocumentFiles(prev => prev.filter(f => f.fileName !== fileData.fileName));
            reject(error);
          }
        }
      }).catch(error => {
        message.open({
          type: "error",
          content: "文件转换失败: " + (error instanceof Error ? error.message : "未知错误"),
        });
        // 上传失败时，从文档文件列表中移除
        setDocumentFiles(prev => prev.filter(f => f.fileName !== fileData.fileName));
        reject(error);
      });
    } else {
      // 使用新的uploadFileToServer方法上传原始文件
      try {
          uploadFileToServer(file).then((result: any) => {
            if (result && result.name) {
              const res = result as any;
              res.fileName = fileData.fileName; // 保持原始文件名
              res.libName = fileData.fileName; // 显示名称使用原始文件名
              res.libDesc = fileData.libDesc;
              res.flag = "file";
              res.fileType = fileData.fileType || 'document';
              res.url = URL.createObjectURL(file);
              
              const updatedFile: FileData = {
                fileName: res.fileName || fileData.fileName,
                libName: res.libName || fileData.fileName,
                libDesc: res.libDesc || fileData.libDesc,
                flag: res.flag || "file",
                fileType: res.fileType || fileData.fileType || 'document',
                url: res.url,
                loading: false,
                ...res
              };
              
              // 根据文件类型更新对应的文件列表
              if (fileData.fileType === 'image') {
                setImageFiles((prev: FileData[]) => {
                  const updatedFiles = prev.filter((f: FileData) => f.fileName !== fileData.fileName);
                  updatedFiles.push(updatedFile);
                  return updatedFiles;
                });
              } else {
                setDocumentFiles((prev: FileData[]) => {
                  const updatedFiles = prev.filter((f: FileData) => f.fileName !== fileData.fileName);
                  updatedFiles.push(updatedFile);
                  return updatedFiles;
                });
              }
              
              // 更新所有文件列表
              setAllFiles((prev: FileData[]) => {
                const updatedFiles = prev.filter((f: FileData) => f.fileName !== fileData.fileName);
                updatedFiles.push(updatedFile);
                return updatedFiles;
              });
              
              message.success("文件上传成功");
              resolve();
            } else {
              message.open({
                type: "error",
                content: "上传失败",
              });
              // 根据文件类型从对应的文件列表中移除
              if (fileData.fileType === 'image') {
                setImageFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== fileData.fileName));
              } else {
                setDocumentFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== fileData.fileName));
              }
              reject(new Error("上传失败"));
            }
          }).catch((error: unknown) => {
            message.open({
              type: "error",
              content: "上传失败: " + (error instanceof Error ? error.message : "未知错误"),
            });
            // 根据文件类型从对应的文件列表中移除
            if (fileData.fileType === 'image') {
              setImageFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== fileData.fileName));
            } else {
              setDocumentFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== fileData.fileName));
            }
            reject(error);
          });
      } catch (error) {
        message.open({
          type: "error",
          content: "上传失败: " + (error instanceof Error ? error.message : "未知错误"),
        });
        // 根据文件类型从对应的文件列表中移除
        if (fileData.fileType === 'image') {
          setImageFiles(prev => prev.filter(f => f.fileName !== fileData.fileName));
        } else {
          setDocumentFiles(prev => prev.filter(f => f.fileName !== fileData.fileName));
        }
        reject(error);
      }
    }
          })();
        });
      });
    };

    const handleDeleteFile = (file?: FileData) => {
      if (file) {
        // 删除单个文件
        // 根据文件类型从对应的文件列表中移除
        if (file.fileType === 'image') {
          setImageFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== file.fileName));
        } else {
          setDocumentFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== file.fileName));
        }
        
        // 从所有文件列表中移除
        setAllFiles((prev: FileData[]) => prev.filter((f: FileData) => f.fileName !== file.fileName));
        message.info("已删除文件");
      } else {
        // 清空所有文件列表
        setDocumentFiles([]);
        setImageFiles([]);
        setAllFiles([]);
        setLocalFile([]);
        message.info("已删除所有文件");
      }
    };

    const fileExtensionHandler = (item: any) => {
      // 优先使用原始文件类型，如果没有则使用当前文件类型
      const extension = (item.originalFileType || item.libDesc).toLowerCase();
      let iconElement = null;
      
      // 根据文件类型返回不同的图标
      if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
        iconElement = <span className="file-icon image-icon">图片</span>;
      } else if (extension === "pdf") {
        iconElement = <span className="file-icon pdf-icon">PDF</span>;
      } else if (["docx", "doc"].includes(extension)) {
        iconElement = <span className="file-icon word-icon">Word</span>;
      } else if (["xls", "xlsx", "csv"].includes(extension)) {
        iconElement = <span className="file-icon excel-icon">Excel</span>;
      } else if (extension === "txt") {
        iconElement = <span className="file-icon txt-icon">TXT</span>;
      } else if (extension === "pptx") {
        iconElement = <span className="file-icon ppt-icon">PPT</span>;
      } else {
        iconElement = <span className="file-icon default-icon">文件</span>;
      }
      
      return iconElement;
    };

    useEffect(() => {
      // 合并文档和图片文件列表到allFiles
      setAllFiles([...documentFiles, ...imageFiles]);
    }, [documentFiles, imageFiles]);

    useEffect(() => {
      onQueryChange?.(query);
    }, [query, onQueryChange]);

    useImperativeHandle(ref, () => ({
      getInputData: () => {
        return {
          documentFiles,
          imageFiles,
          allFiles,
          query,
        };
      },
      setInputData: (data: any) => {
        if (data.documentFiles !== undefined) setDocumentFiles(data.documentFiles);
        if (data.imageFiles !== undefined) setImageFiles(data.imageFiles);
        if (data.allFiles !== undefined) setAllFiles(data.allFiles);
        if (data.query !== undefined) setQuery(data.query);
      },
    }));

    return (
      <Flex className="document-input-module" vertical>
        <Flex className="text-input" vertical>
          <Flex className="input-textarea" vertical>
            <TextArea
              autoFocus
              className="text-input-area"
              placeholder="我需要你帮我起草一篇 【请填写文书类型】 ，起草要求是 【请说明您的起草要求】"
              rows={4}
              value={query}
              maxLength={10000}
              onChange={(e) => {
                const value = e.target.value;
                // 检查内容是否只包含空格或回车符
                if (/^\s*$/.test(value)) {
                  setQuery(""); // 如果只包含空格或回车符，清空输入框
                } else {
                  setQuery(value); // 否则更新输入内容
                }
              }}
            />
            <Flex vertical style={{ padding: "0px 12px 12px" }}>
              <Divider
                orientation="left"
                style={{
                  color: csstoken.colorTextTertiary,
                  fontSize: "10px",
                  margin: "0px",
                  fontWeight: "normal",
                }}
              >
                素材参考
              </Divider>
              <Flex
                className="top-toolbar"
                justify="space-between"
                align="center"
              >
                <Flex className="top-toolbar-left" align="center">
                  <Flex align="center" gap={csstoken.marginXXS}>
                    <Flex className="upload-file">
                      <Upload
                        {...uploadProps}
                        customRequest={handleCustomRequest}
                        className="document-upload-file"
                      >
                        <Button
                          type="text"
                          icon={<FolderAddOutlined className="icon" />}
                          className="btn-icon"
                        >
                          上传文件
                        </Button>
                      </Upload>
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
              <Flex
                className={classNames({ "file-list": allFiles.length > 0 })}
              >
                <Flex className="file-list-container" vertical>
                  {allFiles && allFiles.length > 0 && allFiles.map((file: FileData, index: number) => (
                    <Flex
                      key={index}
                      className="file-item-container"
                      style={{ width: "100%", marginBottom: index < allFiles.length - 1 ? "8px" : "0" }}
                    >
                      {file.loading && (
                        <Flex className="file-loading" align="center">
                          <Spin spinning={file.loading}></Spin>
                        </Flex>
                      )}
                      <Flex
                        className="file-item-content"
                        align="center"
                        style={{
                          width: "100%",
                        }}
                      >
                        <Flex
                          className="file-item-info"
                          align="center"
                        >
                          {fileExtensionHandler(file)}
                          <Flex
                            className="file-details"
                            vertical
                            justify="center"
                          >
                            <div className="file-name">
                              {file.libName}
                            </div>
                            <div className="file-type">{file.libDesc} ({file.fileType})</div>
                          </Flex>
                        </Flex>
                        {!file.loading && (
                          <CloseCircleFilled
                            className="delete-icon"
                            onClick={() => handleDeleteFile(file)}
                          />
                        )}
                      </Flex>
                    </Flex>
                  ))}
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    );
  }
);

export default DocumentInputModule;