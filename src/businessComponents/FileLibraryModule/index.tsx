import { 
  Flex, 
  Typography, 
  Card,
  Input,
  Popconfirm
} from "antd";
import React, {
  forwardRef, 
} from "react";
import { 
  DeleteOutlined, 
  SearchOutlined,
  EditOutlined,
} from "@ant-design/icons";

interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  pageInfo: any; // 当前的步骤信息
  onIncrement: () => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  currentEchoData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  entityViewData: {
    messages: string;
    chunks: any[]; // 切分后的数据
    fileList: []; // 文件列表
    isQuentially?: boolean; // 是否提取实体
    onDataReady?: (data: any) => void;
  };
  uploadedFiles?:any
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const FileLibraryModule = forwardRef<MentionsComponentRef, MentionsComponentProps>(
  ({
    // eslint-disable-next-line no-empty-pattern
    uploadedFiles:[]
  }) => {
    return (
      <Card className="legal-page-library">
        <Typography.Title className="file-title" level={5}>
          入库方式
        </Typography.Title>
        <Flex style={{ marginBottom: "24px" }}>
          <Input
            prefix={<SearchOutlined />}
            placeholder="搜索知识库"
            disabled={true}
          />
        </Flex>
        {/* 文件列表 */}
        {uploadedFiles.length > 0 && (
          <div className="file-list" style={{ maxHeight: "550px" }}>
            <Typography.Text
              className="section-title"
              style={{ display: "block" }}
            >
              文档列表
            </Typography.Text>
            {uploadedFiles.map((file: any) => (
              <div key={file.id} className="file-item">
                <span className="file-item-name">{file.name}</span>
                <div className="file-item-icon">
                  <div className="file-item-ledge">
                    <img src="" alt="" />
                    <span>知识库名称</span>
                  </div>
                  <EditOutlined
                    style={{ marginRight: "10px", cursor: "pointer" }}
                  />
                  <Popconfirm
                    title="删除确认"
                    description="确认删除该文档吗？"
                    onConfirm={() => handleDelete(file.id)}
                    // onCancel={cancel}
                    okText="确认"
                    cancelText="取消"
                  >
                    <DeleteOutlined
                      style={{ color: "#ff4d4f", cursor: "pointer" }}
                    />
                  </Popconfirm>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  }
);

FileLibraryModule.displayName = "FileLibraryModule";
export default FileLibraryModule;
