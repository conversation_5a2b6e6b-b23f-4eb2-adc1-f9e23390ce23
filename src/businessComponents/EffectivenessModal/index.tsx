// 错别字与用语核查
import { Col, Row, Tabs, Flex, message, theme } from "antd";
import type { TabsProps } from "antd";
import {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import "./index.less";
import useSSEChat from "@/hooks/useSSEChat";
import { fileToBase64 } from "@/utils/common";
import DocxPreview, { DocxPreviewRef } from "@/component/DocxViewer";
import { NoData } from "@/component/NoData";
import StreamTypewriter from "@/component/StreamTypewriter";
import { getToken, getUserInfo } from "@/utils/auth";
import { uploadChatFile } from "@/api/public";

interface MentionsComponentProps {
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  agentId: string; // agentId
  unitInputData: {
    originalFile: any; // 原始文件
    fileData: any; // 文件信息
    queryData?: string; // 输入框的值
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const { useToken } = theme;
const EffectivenessModal = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        originalFile: null,
        fileData: null,
        queryData: "",
      },
      agentId,
      setGlobalLoading,
      unitOutPutData,
    },
    ref
  ) => {
    const { token } = useToken();
    const sseChat = useSSEChat();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [currentExtractModal, setCurrentExtractModal] = useState("1");
    const [highlightWords, setHighlightWords] = useState<string[]>([]); // 高亮的文本
    const [messageStr, setMessageStr] = useState(""); // 流式的内容
    const [useTypewriter, setUseTypewriter] = useState(true); // 是否使用打字机效果
    const scrollRef = useRef<HTMLDivElement>(null);
    const docxPreviewRef = useRef<DocxPreviewRef>(null);

    useEffect(() => {
      if (
        unitOutPutData?.effectivenessData &&
        unitOutPutData?.effectivenessData.length > 0
      ) {
        setUseTypewriter(false);
        setMessageStr(unitOutPutData?.effectivenessData);
      } else {
        getChunks();
      }
    }, []);

    // 拿到识别的数据
    const getChunks = async () => {
      setGlobalLoading?.(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      setMessageStr("");
      const fileData = {
        fileName: unitInputData?.originalFile[0]?.name,
        fileStr: await fileToBase64(unitInputData?.originalFile[0]),
        loading: true,
        path: "/files/upload",
        agentId,
        user: userInfo?.id,
      };
      setUseTypewriter(true);
      uploadChatFile(fileData).then(async (response: any) => {
        if (response.code == 200) {
          const fileInfo = {
            type: response?.data?.fileType || "document",
            transfer_method: "local_file",
            upload_file_id: response?.data?.id,
          };
          sseChat.start({
            url: "/dify/broker/agent/stream",
            headers: {
              "Content-Type": "application/json",
              Token: tokenInfo || "",
            },
            body: {
              insId: "1",
              bizType: "app:agent",
              bizId: agentId || "",
              agentId: agentId || "",
              path: "/chat-messages",
              difyJson: {
                inputs: {},
                response_mode: "streaming",
                user: userInfo?.id || "anonymous",
                conversation_id: "",
                query: unitInputData?.queryData || "1",
                files: [fileInfo],
              },
            },
            query: {},
            onMessage: (text: string) => {
              // 流式更新文本
              setMessageStr(text);
            },
            onFinished: (dataVal: any) => {
              setGlobalLoading?.(false);
            },
          });
        } else {
          message.open({
            key: "uploading",
            type: "error",
            content: "文件上传失败",
            duration: 1,
          });
        }
      });
    };
    const tabItems: TabsProps["items"] = [
      {
        key: "1",
        label: "识别区",
      },
    ];

    // 高亮并滚动到指定位置
    const highlight = (item: any) => {
      if (!item?.title) return;

      // 使用 DocxPreview 的 highlightAndScroll 方法
      if (docxPreviewRef.current) {
        docxPreviewRef.current.highlightAndScroll(item.title);
      }

      // 同时更新 highlightWords 状态以保持一致性
      setHighlightWords((prevWords) => {
        if (prevWords.includes(item.title)) {
          return prevWords;
        }
        return [...prevWords, item.title];
      });
    };

    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          effectivenessData: messageStr,
        };
      },
      getEffectivenessData: () => {
        console.log("到第一步了");
        getChunks();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="effectiveness">
        <Row style={{ height: "100%" }}>
          <Col md={12}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="split-file-list"
              items={unitInputData?.originalFile?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div
                    style={{
                      minHeight: "calc(100vh - 215px)",
                      overflowY: "auto",
                    }}
                  >
                    <DocxPreview
                      ref={docxPreviewRef}
                      file={unitInputData?.originalFile[currentActiveKey]}
                      className="my-split-docx-preview"
                      style={{ minHeight: "calc(-215px + 100vh)" }}
                      highlightWords={highlightWords}
                      onHighlightScroll={(word) => {
                        console.log(`滚动到高亮位置: ${word}`);
                      }}
                    />
                  </div>
                ),
              }))}
            />
          </Col>
          <Col xs={24} md={12} className="right">
            <Flex justify="space-between" align="center">
              <Tabs
                defaultActiveKey="1"
                items={tabItems}
                className="split-file-view"
                onChange={(e) => {
                  setCurrentExtractModal(e);
                }}
                activeKey={currentExtractModal}
              />
            </Flex>

            {currentExtractModal == "1" && (
              <Row
                style={{
                  height: "calc(100vh - 213px)",
                  overflowY: "auto",
                  paddingTop: "8px",
                  paddingBottom: "20px",
                }}
                ref={scrollRef}
              >
                {messageStr ? (
                  <StreamTypewriter
                    useTypewriter={useTypewriter}
                    text={messageStr}
                    onchange={() => {
                      scrollRef.current?.scrollTo({
                        top: scrollRef.current.scrollHeight,
                        behavior: "smooth",
                      });
                    }}
                    end={true}
                    charsPerUpdate={5}
                  />
                ) : (
                  <NoData />
                )}
              </Row>
            )}
          </Col>
        </Row>
      </div>
    );
  }
);
// 添加 displayName
EffectivenessModal.displayName = "SpellCheck";
export default EffectivenessModal;
