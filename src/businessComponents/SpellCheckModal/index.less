.spell-check{
  .split-file-list{
    background: var(--ant-color-bg-container);
    padding: 0px 35px 0px 50px;
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      margin-bottom: 18px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-content{
      height: calc(100vh - 215px);
      overflow: auto;
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .split-file-view{
    background: var(--ant-color-bg-container);
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .right{
    height: calc(100vh - 148px);
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
    .split-file-view{
      .ant-tabs-ink-bar{
        // width:0px !important;
      }
    }
  }
  .split-card{
    padding: 10px 20px 20px;
    height: 190px;
    .split-card-detail{
      padding: 0px;
    }
    .ant-card-head{
      padding: 0px;
      min-height: 42px !important;
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      line-height: 22px;
      color: #333333;
      border-bottom:0px;
    }
    .ant-card-body{
      padding: 0px;
      margin-top:var(--ant-margin-xs);
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      color: #333333;
    }
    &:hover{
      border: 1px solid var(--ant-color-primary);
    }
  }
}
.my-split-docx-preview-wrapper{
  background: var(--ant-color-bg-container) !important;
  // height: calc(100vh - 215px);
  padding: 20px !important;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  margin: 5px;
  .docx-wrapper>section.docx{
    // padding: 10px 20px;
  }
  .my-split-docx-preview{
    width: 100% !important;
    margin-bottom:0px !important;
    // padding: 0px !important;
    // height: calc(100vh - 215px);
    // overflow: auto;
    // padding: 20px !important;
    box-shadow: none !important;
    min-height:0px !important;

    // 高亮样式
    .docx-highlight {
      background-color: #ffeb3b !important;
      padding: 2px 4px !important;
      border-radius: 2px !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
      transition: all 0.3s ease !important;

      &:hover {
        background-color: #ffc107 !important;
      }
    }
  }
}
 