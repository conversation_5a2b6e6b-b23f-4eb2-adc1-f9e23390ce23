export const getFileType = (filename: string) => {
  const mimeType = filename.split(".").pop();
  switch (mimeType) {
    case "docx":
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    case "txt":
      return "text/plain";
    case "csv":
      return "text/csv";
    case "xls":
      return "application/vnd.ms-excel";
    case "pptx":
      return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
    case "pdf":
      return "application/pdf";
    case "doc":
      return "application/msword";
    case "ppt":
      return "application/vnd.ms-powerpoint";
    case "xlsm":
      return "application/vnd.ms-excel.sheet.macroEnabled.12";
    case "xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    case "xlsb":
      return "application/vnd.ms-excel.sheet.binary.macroEnabled.12";

    default:
      return "text/plain";
  }
};

// 文件大小转换
export const formatSize = (size: number): string => {
  if (size < 1024) return size + " B"; // 小于 1KB
  else if (size < 1024 * 1024)
    return (size / 1024).toFixed(2) + " KB"; // 小于 1MB
  else if (size < 1024 * 1024 * 1024)
    return (size / 1024 / 1024).toFixed(2) + " MB"; // 小于 1GB
  else return (size / 1024 / 1024 / 1024).toFixed(2) + " GB"; // 更大
};
