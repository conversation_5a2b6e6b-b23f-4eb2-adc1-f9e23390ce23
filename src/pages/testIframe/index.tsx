import React from "react";

const innerMostIframeContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial; padding: 20px; background: #e6f7ff; }
    p { margin: 12px 0; }
  </style>
</head>
<body>
  <h4>最内层 iframe</h4>
  <p>在这里可以划词测试，确保三层嵌套坐标正确。</p>
  <p>再多写几行，保证有滚动条。</p>
  <p>再多写几行，保证有滚动条。</p>
</body>
</html>
`;

const middleIframeContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial; padding: 20px; background: #fffbe6; }
    .wrapper { border: 2px dashed red; padding: 10px; }
  </style>
</head>
<body>
  <h3>中间 iframe</h3>
  <div class="wrapper">
    <iframe
      style="width:100%; height:300px; border:2px solid purple;"
      src="https://onedrive.live.com/edit?id=0702926D5D4422CA!1188&resid=0702926D5D4422CA!1188&ithint=file,docx&wdOrigin=APPHOME-WEB.DIRECT,APPHOME-WEB.JUMPBACKIN&wdPreviousSession=fc5a2c54-fb76-4bf1-acf7-662f25990f1f&wdPreviousSessionSrc=AppHomeWeb&ct=1757670158691&wdo=2&cid=0702926d5d4422ca"
    ></iframe>
  </div>
</body>
</html>
`;

const outerIframeContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial; padding: 20px; background: #f6ffed; }
    .wrapper { border: 2px dashed green; padding: 10px; }
  </style>
</head>
<body>
  <h2>外层 iframe</h2>
  <div class="wrapper">
    <iframe
      style="width:100%; height:400px; border:2px solid orange;"
      srcdoc='${middleIframeContent.replace(/\n/g, "").replace(/'/g, "&apos;")}'
    ></iframe>
  </div>
</body>
</html>
`;

export default function App() {
  return (
    <div style={{ padding: 20 }}>
      <h1>React 三层 iframe 嵌套示例</h1>
      <iframe
        style={{ width: "100%", height: "600px", border: "2px solid blue" }}
        srcDoc={outerIframeContent}
      />
    </div>
  );
}
