
.local-file-page{
  overflow-x:auto;
  .local-file-page-con{
    min-width: 1300px;
  }
  .fixed-bottom-chat{
    position: fixed;
    background: var(--ant-color-bg-elevated);
    padding: var(--ant-padding-xs);
    right: 16px;
    bottom: 16px;
    z-index: 20;
    border-radius: var(--ant-border-radius-lg);
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.08),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 9px 28px 8px rgba(0, 0, 0, 0.05);
    .fixed-bottom-operate{
      padding: 5px 8px;
      height: 44px;
      margin-top:2px;
      span{
        color: var(--ant-color-text);
        font-size: var(--ant-font-size);
        line-height: var(--ant-font-size);
        text-align: center;
        .anticon{
          font-size: var(--ant-font-size-lg)
        }
      }
      span:nth-child(2){
        margin-top:4px;
      }
    }
    .operate-hover{
      cursor: pointer;
      &:hover{
        border-radius: var(--ant-border-radius-sm);
        background: var(--ant-color-bg-text-hover)
      }
    }
    .fixed-bottom-line{
      margin-top: 5px;
      width: 1px;
      height: 44px;
      background: rgba(0, 0, 0, 0.06);
    }
    .fixed-bottom-close{
      cursor: pointer;
      margin-top:2px;
      font-size: var(--ant-font-size-xl);
      color: var(--ant-color-icon)
    }
  }
}