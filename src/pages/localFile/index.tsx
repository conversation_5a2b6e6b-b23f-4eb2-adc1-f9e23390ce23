import React, { useEffect, useMemo, useState } from "react";
import {
  Layout,
  Input,
  Row,
  Col,
  Typography,
  Card,
  Space,
  Tree,
  Flex,
  Badge,
  Checkbox,
  Button,
  message,
  Tag,
  Tooltip,
  Spin,
  Empty,
} from "antd";
import { formatSize } from "@/utils/file";
import {
  onlinePreviewUrl,
  bindedList,
  getBindInfoByLibId,
} from "@/api/knowledge";
import IconFont from "@/component/IconFont";
import HeaderCom from "@/component/Header";
import KnowLedge from "@/component/knowledge";
import {
  SearchOutlined,
  FolderOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileMarkdownOutlined,
  FilePptOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
} from "@ant-design/icons";
import type { TreeDataNode } from "antd";
import Title from "antd/es/typography/Title";
import "./index.less";

const { Sider, Content } = Layout;
const { Text } = Typography;
const { DirectoryTree } = Tree;
const { Search } = Input;

// 递归过滤树节点
const filterTree = (nodes: TreeDataNode[], search: string): TreeDataNode[] => {
  return nodes
    .map((node) => {
      const match = node.title.toLowerCase().includes(search.toLowerCase());
      if (node.children) {
        const filteredChildren = filterTree(node.children, search);
        if (match || filteredChildren.length) {
          return { ...node, children: filteredChildren };
        }
      } else if (match) {
        return node;
      }
      return match ? node : null;
    })
    .filter(Boolean) as TreeDataNode[];
};

const LocalFileAssistant = () => {
  const [searchValue, setSearchValue] = useState("");
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]); // 树结构数据
  const [selectedPath, setSelectedPath] = useState<string>(""); // 选中的路径
  const [selectedFile, setSelectedFile] = useState<any>(null); // 树选中的数据
  const [checkedFiles, setCheckedFiles] = useState<any[]>([]); // 选中的卡片数据
  const [filesListState, setFilesListState] = useState([]); // 文件列表数据接口传进来的
  const [knowledModel, setKnowledModel] = useState(false); // 选择知识库弹框
  const [searchKey, setSearchKey] = useState(""); // 当前文件夹下的搜索
  const [cardData, setCardData] = useState<any[]>([]); // 知识库卡片数据
  const [globalLoading, setGlobalLoading] = useState(false);
  const [viewMode, setViewMode] = useState<"card" | "list">("card");

  // 获取知识库卡片数据
  const getCardData = () => {
    bindedList({}).then((res: any) => {
      console.log(res, 111);
      setCardData(res.data);
    });
  };

  // 选择知识库
  const selectKnowFun = async () => {
    await getCardData();
    setKnowledModel(true);
  };
  // 左侧树选中
  const onSelect = (keys: React.Key[], info: any) => {
    if (keys.length) setSelectedPath(keys[0] as string);
    else setSelectedPath("");
    setSelectedFile(info.node);
  };

  const displayedFiles = useMemo(() => {
    let filtered = filesListState;

    // 根据选中文件夹路径过滤
    if (selectedPath) {
      filtered = filtered.filter((file) =>
        file.path.replace(/\\/g, "/").startsWith(selectedPath)
      );
    }

    // 根据搜索关键词过滤
    if (searchKey) {
      filtered = filtered.filter((file) =>
        file.name.toLowerCase().includes(searchKey.toLowerCase())
      );
    }

    return filtered;
  }, [selectedPath, filesListState, searchKey]);
  const onExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys as string[]);
  };

  const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // 可选：输入搜索时自动展开匹配节点
    if (value) {
      const expandKeys: string[] = [];
      const traverse = (nodes: TreeDataNode[]) => {
        nodes.forEach((node) => {
          if (node.children) {
            traverse(node.children);
          }
          if (node.title.toLowerCase().includes(value.toLowerCase())) {
            expandKeys.push(node.key.toString());
          }
        });
      };
      traverse(treeData);
      setExpandedKeys(expandKeys);
    } else {
      setExpandedKeys([]);
    }
  };
  // 组装树结构数据
  function buildTree(files: any) {
    const tree: any[] = [];

    files.forEach((file) => {
      const parts = file.path.replace(/\\/g, "/").split("/");

      // 去掉最后一级（文件名）
      const folderParts = parts.slice(0, -1);

      let currentLevel = tree;

      folderParts.forEach((part, index) => {
        let existingNode = currentLevel.find((node) => node.title === part);

        if (!existingNode) {
          existingNode = {
            title: part,
            key: folderParts.slice(0, index + 1).join("/"),
            children: [],
          };
          currentLevel.push(existingNode);
        }

        currentLevel = existingNode.children;
      });
    });

    return tree;
  }
  // 将 /Date(时间戳)/ 转为本地可读时间
  const formatUtcDate = (utcString: string) => {
    const timestamp = parseInt(utcString.match(/\d+/)?.[0] || "0");
    return new Date(timestamp).toLocaleString(); // 本地时间
  };

  // 所有的文件信息
  const getFileIcon = (filename: string) => {
    const ext = filename.split(".").pop()?.toLowerCase();

    switch (ext) {
      case "folder":
        return <FolderOutlined style={{ color: "#1890ff", fontSize: 32 }} />;
      case "pdf":
        return <FilePdfOutlined style={{ color: "#f50", fontSize: 32 }} />;
      case "xls":
      case "xlsx":
        return <FileExcelOutlined style={{ color: "#52c41a", fontSize: 32 }} />;
      case "doc":
      case "docx":
        return <FileWordOutlined style={{ color: "#1890ff", fontSize: 32 }} />;
      case "ppt":
      case "pptx":
        return <FilePptOutlined style={{ color: "#fa8c16", fontSize: 32 }} />;
      case "png":
      case "jpg":
      case "jpeg":
      case "gif":
      case "bmp":
      case "svg":
        return <FileImageOutlined style={{ color: "#eb2f96", fontSize: 32 }} />;
      case "mp3":
      case "wav":
      case "ogg":
      case "flac":
        return <FileOutlined style={{ color: "#722ed1", fontSize: 32 }} />;
      case "zip":
      case "rar":
      case "7z":
      case "tar":
      case "gz":
        return <FileZipOutlined style={{ color: "#fa541c", fontSize: 32 }} />;
      case "txt":
        return <FileTextOutlined style={{ color: "#8c8c8c", fontSize: 32 }} />;
      case "md":
        return (
          <FileMarkdownOutlined style={{ color: "#1890ff", fontSize: 32 }} />
        );
      default:
        return <FileOutlined style={{ fontSize: 32 }} />;
    }
  };

  // 复选框change
  const handleCheckChange = (e, item) => {
    const checked = e.target.checked;

    setFilesListState((prev) =>
      prev.map((file) =>
        file.path === item.path ? { ...file, isChecked: checked } : file
      )
    );
    setCheckedFiles((prev) => {
      if (checked) {
        // ✅ 勾选：加入数组（去重）
        console.log([...prev, item]);
        return [...prev, item];
      } else {
        // ❌ 取消勾选：移除
        return prev.filter((f) => f.path !== item.path);
      }
    });
  };

  // 预览
  const handlePreview = (item: any) => {
    // if (item?.ossId) {
    //   onlinePreviewUrl(item?.ossId).then((res: any) => {
    //     if (res.code == 200) {
    //       window.open(res.data, "_blank");
    //     }
    //   });
    // }
    if (item?.knowId) {
      onlinePreviewUrl(item.knowId).then((res: any) => {
        if (res.code == 200) {
          window.open(res.data, "_blank");
        }
      });
    } else {
      message.warning("该文件不可预览");
      return;
    }
  };
  // 问答
  const onQute = () => {
    if (checkedFiles.length > 5) {
      message.warning("最多只能选择5个文件");
      return;
    }
    if (checkedFiles.length === 0) {
      message.warning("请选择文件");
      return;
    }
    window.postMessage(
      {
        type: "chatKnowInfo",
        fileKnowData: checkedFiles,
      },
      "*"
    );
  };
  // 保存到知识库
  const addKnow = () => {
    const hasAlreadySaved = filesListState.some((file) => file.ifKnow);

    if (hasAlreadySaved) {
      message.warning("选择错误：部分文件已存入知识库！");
      return; // 阻止后续操作
    }
  };

  // 知识库弹框点击确定
  const closeKnowledModel = (type, data) => {
    setGlobalLoading(true);
    setKnowledModel(type);
    getBindInfoByLibId({ libId: String(data) }).then((res) => {
      if (res.code == 200) {
        const data = res?.data?.libBindLocalDetailList || [];
        const withChecked = data.map((file) => ({
          ...file,
          isChecked: false, // 转成布尔值
        }));

        setFilesListState(withChecked);

        // 模拟异步数据加载
        const treeData = buildTree(withChecked);
        setTreeData(treeData);
        setGlobalLoading(false);
      }
    });
  };

  // 知识库取消
  const closeKnowledModelFalg = () => {
    setKnowledModel(false);
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Layout
        style={{ minHeight: "100vh", background: "#fff" }}
        className="local-file-page"
      >
        <div className="local-file-page-con">
          {/* 顶部标题和描述 */}
          <HeaderCom
            mainTitle="本地文件助手"
            subTitle="检索，预览，同步知识库"
          />
          <Layout
            style={{ margin: "10px 20px", background: "#fff" }}
            className="local-file-page-con"
          >
            {/* 左侧导航菜单 */}
            <Sider
              width={350}
              style={{
                marginLeft: "10px",
                background: "white",
                height: "calc(100vh - 150px)",
                padding: "16px",
                borderRadius: 8,
                boxShadow: "0px 0px 6px 2px rgba(0,0,0,0.05)",
              }}
            >
              <Flex
                justify="space-between"
                align="center"
                style={{ flex: 1, marginBottom: "16px" }}
              >
                <Title
                  level={4}
                  style={{
                    margin: 0,
                    display: "flex",
                    alignItems: "center",
                    flexShrink: 0,
                  }}
                >
                  <div
                    style={{
                      width: 4,
                      height: 16,
                      background: "#1890ff",
                      marginRight: 8,
                      borderRadius: 2,
                    }}
                  ></div>
                  目录结构
                </Title>
                <Button type="primary" onClick={selectKnowFun}>
                  选择知识库
                </Button>
              </Flex>
              <Search
                placeholder="搜索节点"
                style={{ marginBottom: 8 }}
                value={searchValue}
                onChange={onSearchChange}
                allowClear
              />
              {treeData && treeData.length > 0 ? (
                <DirectoryTree
                  style={{
                    overflowY: "auto",
                    height: "calc(100vh - 265px)",
                  }}
                  multiple={false}
                  defaultExpandAll
                  expandedKeys={expandedKeys}
                  onSelect={onSelect} // 改成新方法
                  onExpand={onExpand}
                  treeData={filterTree(treeData, searchValue)} // 用 filterTree 保持搜索功能
                />
              ) : (
                <div style={{ marginTop: "100px", width: "100%" }}>
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </Sider>

            {/* 右侧内容区域 */}
            <Layout
              style={{
                marginLeft: "24px",
                height: "calc(100vh - 150px)",
                boxShadow: "0px 0px 6px 2px rgba(0,0,0,0.05)",
              }}
            >
              <Content
                style={{
                  background: "white",
                  padding: 24,
                  margin: 0,
                  borderRadius: 8,
                }}
              >
                {/* 搜索区域 */}
                <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                  <Flex
                    justify="space-between"
                    align="center"
                    style={{ flex: 1 }}
                  >
                    <Title
                      level={4}
                      style={{
                        margin: 0,
                        display: "flex",
                        alignItems: "center",
                        flexShrink: 0,
                      }}
                    >
                      <div
                        style={{
                          width: 4,
                          height: 16,
                          background: "#1890ff",
                          marginRight: 8,
                          borderRadius: 2,
                        }}
                      ></div>
                      {`${selectedFile?.title || ""}文件信息`}
                    </Title>
                    <Flex gap={20} align="center">
                      <Input
                        placeholder="请输入文件名称"
                        suffix={<SearchOutlined />}
                        style={{ width: "500px" }}
                        size="large"
                        allowClear
                        value={searchKey}
                        onChange={(e) => setSearchKey(e.target.value)}
                      />
                      <Button
                        onClick={() =>
                          setViewMode(viewMode === "card" ? "list" : "card")
                        }
                        icon={
                          viewMode === "card" ? (
                            <UnorderedListOutlined />
                          ) : (
                            <AppstoreOutlined />
                          )
                        }
                        size="large"
                      />
                      {checkedFiles && checkedFiles.length > 0 && (
                        <Button
                          size="large"
                          icon={
                            <IconFont
                              type="knowledgeBaseOutlined"
                              style={{ color: "#fff" }}
                            />
                          }
                          onClick={addKnow}
                        >
                          保存知识库
                        </Button>
                      )}
                    </Flex>
                  </Flex>
                </Row>
                {/* 文件网格展示 */}
                <div
                  style={{ height: "calc(100vh - 240px)", overflowY: "auto" }}
                >
                  {viewMode === "card" ? (
                    <Row gutter={[24, 24]}>
                      {displayedFiles && displayedFiles.length > 0 ? (
                        <>
                          {displayedFiles.map((item: any, index) => (
                            <Col key={index} xs={12} sm={8} md={6} lg={4}>
                              {item.ifKnow ? (
                                <Badge.Ribbon color="#f9da9d" text={"已存"}>
                                  <div style={{ position: "relative" }}>
                                    {/* 多选框 */}
                                    <Checkbox
                                      checked={item.isChecked} // 你的选中状态
                                      onClick={(e) => {
                                        e.stopPropagation();
                                      }}
                                      onChange={(e) =>
                                        handleCheckChange(e, item)
                                      } // 处理选中逻辑
                                      style={{
                                        position: "absolute",
                                        top: 8,
                                        left: 8,
                                        zIndex: 1,
                                      }}
                                    />
                                    <Card
                                      size="small"
                                      onClick={() => {
                                        handlePreview(item);
                                      }}
                                      style={{
                                        textAlign: "center",
                                        cursor: "pointer",
                                        borderRadius: "8px",
                                        height: "140px",
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                      }}
                                      bodyStyle={{
                                        padding: "12px",
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        flex: 1,
                                      }}
                                    >
                                      <div style={{ marginBottom: "8px" }}>
                                        {getFileIcon(item.extension)}
                                      </div>
                                      <Text
                                        ellipsis={{ tooltip: item.name }}
                                        style={{
                                          fontSize: "13px",
                                          marginBottom: "4px",
                                          width: "100%",
                                        }}
                                      >
                                        {item.name}
                                      </Text>
                                      <Space size={4} direction="vertical">
                                        <Text
                                          type="secondary"
                                          style={{ fontSize: "11px" }}
                                        >
                                          {formatSize(item.size)}
                                        </Text>
                                        <Text
                                          type="secondary"
                                          style={{ fontSize: "11px" }}
                                        >
                                          {item.lastWriteTimeUtc || "--"}
                                        </Text>
                                      </Space>
                                    </Card>
                                  </div>
                                </Badge.Ribbon>
                              ) : (
                                <div style={{ position: "relative" }}>
                                  {/* 多选框 */}
                                  <Checkbox
                                    checked={item.isChecked} // 你的选中状态
                                    onClick={(e) => {
                                      e.stopPropagation();
                                    }}
                                    onChange={(e) => handleCheckChange(e, item)} // 处理选中逻辑
                                    style={{
                                      position: "absolute",
                                      top: 8,
                                      left: 8,
                                      zIndex: 1,
                                    }}
                                  />

                                  <Card
                                    size="small"
                                    onClick={() => {
                                      handlePreview(item);
                                    }}
                                    style={{
                                      textAlign: "center",
                                      cursor: "pointer",
                                      borderRadius: "8px",
                                      height: "140px",
                                      display: "flex",
                                      flexDirection: "column",
                                      justifyContent: "center",
                                    }}
                                    bodyStyle={{
                                      padding: "12px",
                                      display: "flex",
                                      flexDirection: "column",
                                      alignItems: "center",
                                      flex: 1,
                                    }}
                                  >
                                    <div style={{ marginBottom: "8px" }}>
                                      {getFileIcon(item.extension)}
                                    </div>
                                    <Text
                                      ellipsis={{ tooltip: item.name }}
                                      style={{
                                        fontSize: "13px",
                                        marginBottom: "4px",
                                        width: "100%",
                                      }}
                                    >
                                      {item.name}
                                    </Text>
                                    <Space size={4} direction="vertical">
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: "11px" }}
                                      >
                                        {formatSize(item.size)}
                                      </Text>
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: "11px" }}
                                      >
                                        {item.lastWriteTimeUtc || "--"}
                                      </Text>
                                    </Space>
                                  </Card>
                                </div>
                              )}
                            </Col>
                          ))}
                        </>
                      ) : (
                        <div style={{ marginTop: "100px", width: "100%" }}>
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        </div>
                      )}
                    </Row>
                  ) : (
                    <div>
                      {displayedFiles.length > 0 ? (
                        displayedFiles.map((item: any, index) => (
                          <div
                            key={index}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              padding: "12px 12px",
                              borderBottom: "1px solid #f0f0f0",
                              cursor: "pointer",
                            }}
                            onClick={() => handlePreview(item)}
                          >
                            {/* 多选框 */}
                            <Checkbox
                              checked={item.isChecked}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) => handleCheckChange(e, item)}
                              style={{ marginRight: 12 }}
                            />

                            {/* 文件图标 */}
                            <div style={{ marginRight: 12 }}>
                              {getFileIcon(item.extension)}
                            </div>

                            {/* 文件信息 */}
                            <div style={{ flex: 1 }}>
                              <Text
                                ellipsis
                                style={{ fontSize: 14, fontWeight: 500 }}
                              >
                                {item.name}
                              </Text>
                              <div style={{ color: "#999", fontSize: 12 }}>
                                {formatSize(item.size)} ·{" "}
                                {item.lastWriteTimeUtc || "--"}
                              </div>
                            </div>

                            {/* 已存 Badge */}
                            {item.ifKnow && (
                              <Tag
                                color="gold"
                                style={{
                                  marginLeft: 12,
                                  borderRadius: 4,
                                  padding: "0 8px",
                                  fontSize: 12,
                                }}
                              >
                                已存
                              </Tag>
                            )}
                          </div>
                        ))
                      ) : (
                        <div style={{ marginTop: "100px", width: "100%" }}>
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        </div>
                      )}
                    </div>
                  )}
                </div>
                {/* 知识库底部操作栏*/}
                {checkedFiles && checkedFiles.length > 0 && (
                  <Flex className="fixed-bottom-chat" gap={4}>
                    <Flex
                      className="fixed-bottom-operate"
                      vertical
                      align="center"
                    >
                      <span>{checkedFiles?.length || 0}</span>
                      <span style={{ marginTop: "6px" }}>已选</span>
                    </Flex>
                    <div className="fixed-bottom-line"></div>
                    <Flex
                      className="fixed-bottom-operate operate-hover"
                      vertical
                      align="center"
                      onClick={() => {
                        onQute();
                      }}
                    >
                      <span>
                        <IconFont
                          type="AIChatOutlined"
                          isGradien={true}
                          width={16}
                          height={16}
                        />
                      </span>
                      <span>问答</span>
                    </Flex>
                    <div className="fixed-bottom-line"></div>
                    <Flex
                      className="fixed-bottom-close fixed-bottom-operate operate-hover"
                      align="center"
                    >
                      <Tooltip placement="top" title="点击将清空选中项">
                        <span
                          onClick={() => {
                            setFilesListState((prev) =>
                              prev.map((file) => ({
                                ...file,
                                isChecked: false,
                              }))
                            );
                            setCheckedFiles([]);
                          }}
                        >
                          清空
                        </span>
                      </Tooltip>
                    </Flex>
                  </Flex>
                )}
              </Content>
            </Layout>
          </Layout>
          <KnowLedge
            knowledModel={knowledModel}
            cardData={cardData}
            closeKnowledModelFalg={closeKnowledModelFalg}
            closeKnowledModel={closeKnowledModel}
          />
        </div>
      </Layout>
    </>
  );
};

export default LocalFileAssistant;
