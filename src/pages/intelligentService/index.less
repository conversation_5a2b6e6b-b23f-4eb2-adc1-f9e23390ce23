.intelligent-service-layout {
  padding: 15px;
  background-color: rgba(241, 243, 248, 1);
  min-width: 1117px;
}
.header {
  margin-bottom: 15px;
  padding: 0 20px;
  height: 70px;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(187, 187, 187, 1);
  border-radius: 8px;
  .header-space {
    height: 70px;
  }
}
.content-layout {
  padding: 15px;
  height: calc(100vh - 115px);
  background: #f5f5f5;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(187, 187, 187, 1);
  .content-sider {
    .content-search {
      padding: 10px;
      .search-ipt {
        &::placeholder {
          font-size: 12px;
          color: #808080;
        }
      }
    }
    .customer-list {
      padding: 10px;
      max-height: calc(100vh - 200px);
      width: 100%;
      overflow-y: scroll;
      .customer-list-badge {
        width: 100%;
      }
      .customer-list-item {
        display: flex;
        align-items: center;
        padding: 10px;
        width: 100%;
        max-height: 61px;
        cursor: pointer;
        border-left: 3px solid #fff;
        .list-item-icon {
          //   width: 24px;
          //   height: 24px;
          margin-right: 10px;
        }
        .list-item-name {
          width: calc(100% - 50px);
          font-size: 14px;
          font-weight: 500;
          .item-name-desc {
            font-size: 12px;
            font-weight: 400;
            p {
              width: 180px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
      .customer-list-active {
        // background: #e5f0fe;
        border-color: #5390f7;
      }
    }
  }
  .content {
    flex: 1;
    min-width: 470px;
    margin-left: 15px;
    background: #fff;
    border-left: 1px solid #e7e9ec;
    border-right: 1px solid #e7e9ec;
  }
}

.desc {
  font-size: 12px;
  color: 65768C;
}

.content-sider-right {
  .right-tabs {
    .ant-tabs-nav-list {
      width: 100%;
      .ant-tabs-tab {
        flex: 1;
        justify-content: center;
      }
    }
  }
}
.his-trade-modal {
  .his-trade-modal-tit {
    font-size: var(--ant-font-size-xl);
  }
  .his-trade-modal-con {
    .his-trade-modal-icon {
      margin-left: var(--ant-margin-md);
      cursor: pointer;
      color: #9ca3af;
    }
  }
}
.his-trade-content {
  padding: 12px;
  border-top: 1px solid var(--ant-color-border);
  border-bottom: 1px solid var(--ant-color-border);
  .his-trade-content-tags {
    .his-trade-content-tags-item {
      // padding-bottom: var(--ant-padding-md);
      padding: 4px 10px;
      font-size: 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    .his-trade-content-tags-active {
      background: var(--ant-control-item-bg-active);
      color: var(--ant-color-info-active);
    }
  }
}
.his-trade-content-info {
  padding: 20px;
  .his-trade-content-card {
    margin-bottom: 20px;
    width: 100%;
    .his-trade-content-card-item {
      padding: 20px;
      width: 100%;
      border-radius: 8px;
      .his-trade-item-tit {
        font-size: 12px;
      }
      .his-trade-item-price {
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
  .his-trade-content-detail {
    margin-bottom: 16px;
    .ant-card-body {
      padding: 20px 20px 40px;
    }
    .his-trade-content-detail-item {
      .his-trade-content-detail-logo {
        margin-right: 20px;
        width: 40px;
        height: 40px;
        background: #eff6ff;
        border-radius: 5px;
        .detail-logo {
          font-size: 20px;
        }
        .logo-blue {
          color: var(--ant-color-primary);
        }
        .logo-green {
          color: var(--ant-color-success);
        }
      }
      .his-trade-content-detail-logo-green {
        background: #effdf4;
      }
      .his-trade-content-detail-content {
        .detail-content-fir {
          font-size: 16px;
          font-weight: 500;
        }
        .detail-content-desc {
          font-size: 12px;
        }
        .detail-content-data {
          margin-bottom: 8px;
          font-size: 12px;
        }
        .detail-content-step {
          margin-bottom: 8px;
          font-size: 12px;
        }
        .his-trade-content-detail-steps {
          .his-trade-content-detail-steps-item {
            position: relative;
            height: 6px;
            background: #0000000a;
            border-radius: 3px;
            .his-trade-content-detail-steps-name {
              position: absolute;
              top: 10px;
              left: 50%;
              font-size: 10px;
            }
            &:first-child .his-trade-content-detail-steps-name {
              left: 0;
            }
            &:last-child .his-trade-content-detail-steps-name {
              right: 0;
            }
          }
          .his-trade-content-detail-steps-item-finish {
            background: var(--ant-color-primary);
          }
          .his-trade-content-detail-steps-item-green {
            background: var(--ant-color-success);
          }
        }
      }
    }
  }
}
.his-chat-modal {
  padding: 0 10px;
  .his-chat-modal-search {
    margin-bottom: 12px;
  }
  .his-chat-modal-select {
    padding-bottom: 16px;
  }
  .his-chat-modal-chat {
    margin-bottom: 16px;
    border-top: 1px solid var(--ant-color-border);
    border-bottom: 1px solid var(--ant-color-border);

    .session-content-message {
      padding: 15px;
      width: 100%;
      max-height: 500px;
      overflow-y: scroll;
      .message-item {
        margin-bottom: 30px;
        .ant-avatar {
          min-width: 32px;
        }
        .message-desc {
          margin-left: 10px;
          padding: 10px;
          border-radius: 8px;
          height: max-content;
          font-size: 12px;
          background: #f3f4f6;
          max-width: 750px;
          .message-time {
            font-size: 10px;
          }
          .message-business-btn {
            margin-top: 10px;
          }
          .message-upload {
            margin-top: 10px;
            .ant-upload-wrapper {
              width: 100%;
            }
          }
          .message-product {
            margin-top: 10px;
            padding: 5px;
            max-width: 730px;
            background-color: #f9fafb;
            border-radius: 8px;
            .message-product-item {
              padding: 8px;
              background: #fff;
              max-width: 239px;
              .message-product-item-img {
                margin-bottom: 12px;
                width: 100%;
                height: 100px;
                border-radius: 5px;
              }
              .message-product-item-name {
                margin-bottom: 8px;
                .message-product-item-title {
                  color: #000;
                  font-size: 14px;
                  font-weight: 500;
                }
              }
              .message-product-item-desc {
                margin-bottom: 8px;
                font-size: 10px;
                color: #00000073;
              }
              .message-product-item-card {
                margin-bottom: 10px;
                padding-bottom: 16px;
                border-bottom: 1px solid #e7e9ec;
                .message-product-card-item {
                  padding: 8px;
                  background: #f9fafb;
                  border-radius: 5px;
                  font-size: 10px;

                  .message-product-card-item-name {
                    margin-top: 5px;
                  }
                  .message-product-card-item-price {
                    text-align: center;
                    font-weight: 500;
                  }
                }
              }
              .message-product-hot {
                margin-bottom: 10px;
                .message-product-hot-name {
                  margin-bottom: 10px;
                  font-weight: 500;
                }
                .message-product-hot-list-desc {
                  font-size: 10px;
                }
                .message-product-hot-list-item {
                  margin-bottom: 5px;
                }
              }
              .message-product-btn {
                margin-bottom: 10px;
              }
            }
          }
          .message-form {
            margin-top: 10px;
            min-width: 730px;
            .message-form-title {
              margin-bottom: 16px;
              font-size: 16px;
              font-weight: 500;
            }
            .message-form-type {
              margin-bottom: 10px;
              font-size: 14px;
              font-weight: 500;
            }
            .message-form-type-btns {
              margin-bottom: 10px;
              .message-form-btn-item {
                padding: 10px;
                border: 1px solid #e7e9ec;
                border-radius: 4px;
                cursor: pointer;
              }
              .message-form-btn-item-active {
                background: var(--ant-color-primary);
                color: #f9fafb;
              }
            }
          }
        }
      }
      .message-item-right {
        margin-left: auto;
        .message-desc {
          margin-right: 10px;
          background: #eef2ff;
        }
      }
    }
  }
}
