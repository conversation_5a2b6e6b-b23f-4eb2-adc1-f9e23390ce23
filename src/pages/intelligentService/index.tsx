import React, { useState, useEffect, useRef } from "react";
import { Layout } from "antd";
import {
  Flex,
  Typography,
  Space,
  Input,
  Avatar,
  Badge,
  Tabs,
  Modal,
  Row,
  Col,
  Card,
  Tag,
  Pagination,
  DatePicker,
  Select,
  Upload,
  Spin,
  Button,
  Form,
  Radio,
} from "antd";
import "./index.less";
import Session from "@/component/Session";
import {
  UserOutlined,
  BellFilled,
  SearchOutlined,
  CloseOutlined,
  BankFilled,
  CheckCircleOutlined,
  AreaChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
} from "@ant-design/icons";
import { Message } from "@/types/session";
import type { TabsProps } from "antd";
import Enterpriseprofile from "./components/enterpriseprofile";
import ProductRecommend from "./components/productRecommend";
import FileGenerate from "./components/fileGenerate";
import { getToken, getUserInfo } from "@/utils/auth";
import useSSEChat from "@/hooks/useSSEChat";
import dayjs from "dayjs";
import dai from "@/assets/images/customer/dai1.png";

const agentId = "********-e34d-417e-b0fd-30e664c93e71";
const { Header, Sider, Content } = Layout;

const IntelligentService: React.FC = () => {
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const sseChat = useSSEChat();
  const currentUser = "1"; //客服
  const [customerList] = useState([
    {
      id: "1",
      name: "科大讯飞股份有限公司",
      isVIP: true,
      desc: "我想了解一下本月的优惠我想了解一下本月的优惠",
      time: "14:13",
      msgNum: 0,
    },
  ]);
  const [messageList, setMessageList] = useState<Message[]>([]);
  const [messageListHis, setMessageListHis] = useState<Message[]>([]);
  const [sessionBtnList] = useState([{ id: "1", name: "营销话术" }]);
  const [curCustomerId, setCurCustomerId] = useState("1");
  const [hisTradeModalOpen, setHisTradeModalOpen] = useState(false);
  const [hisChatModalOpen, setHisChatModalOpen] = useState(false);
  const [hisTradeCurId, setHisTradeCurId] = useState("1");
  const [hisTradeTagsList] = useState([
    {
      id: "1",
      tagName: "全部",
    },
    {
      id: "2",
      tagName: "自营贷款",
    },
    {
      id: "3",
      tagName: "委托贷款",
    },
    {
      id: "4",
      tagName: "承兑业务",
    },
    {
      id: "5",
      tagName: "保理业务",
    },
  ]);
  const [uploadFileList, setUploadFileList] = useState<any>([]);
  const [tagsType, setTagsType] = useState<any>("");
  const [companyInfo, setCompanyInfo] = useState({});
  const [financialInfo, setFinancialInfo] = useState({});
  const [companyTags, setCompanyTags] = useState<any>([]);
  const [productList, setProductList] = useState<any>([]);
  const [messageStr, setMessageStr] = useState<any>("");
  const [audioContentData, setAudioContentData] = useState<any>("");
  const [audioContent, setAudioContent] = useState("");
  const [defaultMessage, setDefaultMessage] = useState("");
  const [templateJson, setTemplateJson] = useState("");
  const [marketScript, setMarketScript] = useState<any>([]);
  // 业务类型
  const [businessTypeList] = useState([
    {
      id: "1",
      name: "自营贷款",
    },
    {
      id: "2",
      name: "委托贷款",
    },
    {
      id: "3",
      name: "承兑业务",
    },
    {
      id: "4",
      name: "保理业务",
    },
  ]);
  const sessionRef = useRef(null);
  const getData = (name: string) => {
    const data: string = localStorage.getItem(name) as string;
    if (data) {
      return JSON.parse(data);
    } else {
      return [];
    }
  };
  // setMessageList([...getData("messageList")]);

  const setData = (name: string, data: any) => {
    localStorage.setItem(name, JSON.stringify(data));
  };
  const selectCustomer = (id: string) => {
    setCurCustomerId(id);
  };
  const submitMsg = (msg: string) => {
    console.log("submitMsg", msg);
    messageList.push({
      id: generateRandom8Digits(),
      msg,
      msgBelong: "1",
      time: dayjs().format("HH:ss"),
    });
    setMessageList([...messageList]);
    setData("messageList", messageList);
    setData("messageListHis", messageList);
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
  };
  const onChange = (key: string) => {
    console.log(key);
    setTagsType(key);
    if (key == "2") {
      getProductData("产品推荐");
    }
  };

  const openHisTradModal = (status: boolean) => {
    setHisTradeModalOpen(status);
  };
  const openHisChatModalOpen = (status: boolean) => {
    setHisChatModalOpen(status);
  };
  const handleCancelHisTrande = () => {
    setHisTradeModalOpen(false);
  };
  const handleCancelHisChat = () => {
    setHisChatModalOpen(false);
  };
  // 发送产品
  const sendMessage = (item: any) => {
    console.log("kjjjjjjj", item);
    messageList.push({
      id: generateRandom8Digits(),
      msg: "产品推荐",
      msgBelong: "1",
      time: dayjs().format("HH:ss"),
      type: {
        type: "product",
        productList: [{ ...item }],
      },
    });
    setData("messageList", messageList);
    setData("messageListHis", messageList);
    setMessageList([...messageList]);
  };
  const sendMessageAll = (list: any) => {
    console.log("kjjjjjjj", list);
    messageList.push({
      id: generateRandom8Digits(),
      msg: "产品推荐",
      msgBelong: "1",
      time: dayjs().format("HH:ss"),
      type: {
        type: "product",
        productList: [...list],
      },
    });
    setMessageList([...messageList]);
    setData("messageListHis", messageList);
    setData("messageList", messageList);
  };
  const [markeDownId, setMarkeDownId] = useState("");
  // 生成报告-提取模版
  const generateReports = async (id: string) => {
    setMessageStr("");
    setMarkeDownId(generateRandom8Digits());
    console.log(id);
    if (id == "1") {
      // const inputs = {
      //   type: "报告生成",
      //   report_generation: "模板提取",
      // };
      // let results = "";
      // const res = await chatMessage(inputs, "模板提取");
      // console.log("jsjjsjsjs", res);
      // results += res;
      // const cleanRes = results
      //   .replace(/```json\s*|```$/g, "")
      //   .trim()
      //   .replace(/```/g, "")
      //   .trim();
      // console.log("9999999999", cleanRes);
      const cleanRes =
        '{"【此处描述借款企业基本情况，包括：企业名称、注册地址、成立时间、注册资本、法定代表人、主营业务、股权结构、组织架构、经营规模、员工人数、近三年经营情况等，请务必分点描述，以结构化markdown格式生成。】": {"type": 1,"description": "需要填入借款企业基本情况的详细描述"},"【此处描述借款企业现有授信情况、用信情况及本次申报的固定资产贷款具体情况。请分点描述，结构化输出来增强可读性。】": {"type": 1,"description": "需要填入借款企业授信用信及本次贷款情况"},"【此处判断贷款申请申报业务是否合规，包括：企业主体资质合规性核验（营业执照、经营许可、法定代表人身份真实性）、信用风险合规评估（企业征信报告解析、还款能力测算）、信贷用途合法性审查（固定资产投资项目合规性）、反洗钱与反欺诈防控、担保/抵押合规处理。该处描述不得低于400字，请分点描述，结构化输出来增强可读性。】": {"type": 1,"description": "需要填入申报业务合规性审查内容"},"【申报固定资产贷款所需材料有：1、最新年度检验的《营业执照》复印件，需加盖公章。2、《公司章程》、最新版《公司组织结构图》复印件，需加盖公章。3、法定代表人身份复印件及签字样本，需加盖公章。4、近两年度的财务报表（资产负债表、利润表、现金流量表），需加盖公章。5、公司主要账户近6个月的银行对账单。6、同意申请本次贷款的《股东会决议》原件，需全体股东/董事签字盖章。7、项目合规性文件：项目可行性研究报告、政府投资主管部门的核准/备案文件。8、资金筹措方案：项目总投资构成、资本金来源及到位证明、其他资金来源承诺函等。此处判断贷款申请申报材料是否完整。请罗列出已提供的文件名称，再补充说明需要补充的申报资料。请分点描述，结构化输出来增强可读性。】": {"type": 1,"description": "需要填入申报资料完备性审查内容"},"【请从多维度评估借款企业的信用等级和还款能力，对贷款风险进行精准预估，此处描述不得低于200字，请分点描述，结构化输出来增强可读性。】": {"type": 1,"description": "需要填入风险分析内容"},"【此处输入审查意见，请确保审批结果的客观公正，保护银行的利益，同时兼顾借款企业的合理需求。此处描述不得低于300字。请分点描述，结构化输出来增强可读性。】": {"type": 1,"description": "需要填入审查意见内容"}}';
      setTemplateJson(cleanRes);
      generateReportsPreLoan(cleanRes);
    } else if (id == "2") {
      chatSummary();
    } else if (id == "3") {
      recognizeAudio();
    }
  };
  // 生成报告-贷前审查报告
  const generateReportsPreLoan = async (info: string) => {
    setMessageStr("");
    const inputs = {
      type: "报告生成",
      info: info,
      report_generation: "贷前审查报告",
    };
    const files: any = [];
    uploadFileList.forEach((item: any) => {
      files.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    // let results = "";
    // const res = await chatMessage(inputs, "贷前审查报告", files);
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    // let results = "";
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: agentId || "",
        agentId: agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs,
          // pageinfo: pageInfo,
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: "贷前审查报告",
          files,
        },
      },
      query: {},
      onMessage: (res) => {
        console.log("jsjjsjsjs", res);
        const cleanRes = res
          .replace(/```json\s*|```$/g, "")
          .trim()
          .replace(/```/g, "")
          .trim();
        setMessageStr(cleanRes);
        console.log("yyyyyyyyyy", cleanRes);
        setGlobalLoading(false);
      },
      onFinished: () => {},
    });
  };
  // 生成报告-切换类型
  const changeReportTypeValue = () => {
    setMessageStr("");
  };
  //报告生成-会话总结
  const chatSummary = async () => {
    setMessageStr("");
    const inputs = {
      type: "报告生成",
      info: JSON.stringify(messageList),
      report_generation: "会话总结",
    };
    // let results = "";
    // const res = await chatMessage(inputs, "会话总结");

    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    // let results = "";
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: agentId || "",
        agentId: agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs,
          // pageinfo: pageInfo,
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: "会话总结",
        },
      },
      query: {},
      onMessage: (res) => {
        const cleanRes = res
          .replace(/```json\s*|```$/g, "")
          .trim()
          .replace(/```/g, "")
          .trim();
        console.log("oooooooooo", cleanRes);
        setMessageStr(cleanRes);
        setGlobalLoading(false);
      },
      onFinished: () => {},
    });
  };
  // 报告生成-识别语音
  const getAudioContentData = (val: any) => {
    console.log(val);
    setAudioContentData(val);
    setAudioContent(val);
  };
  //
  const defaultMessageData = (val: string) => {
    setDefaultMessage(val);
  };
  // 报告生成-语音质检
  const recognizeAudio = async () => {
    setMessageStr("");
    const inputs = {
      type: "报告生成",
      // info: audioContentData,
      report_generation: "语音质检",
      rule: defaultMessage,
      query: "ASR语音转文字内容",
    };
    // let results = "";
    // const res = await chatMessage(inputs, audioContentData);

    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    // let results = "";
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: agentId || "",
        agentId: agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs,
          // pageinfo: pageInfo,
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: audioContentData,
        },
      },
      query: {},
      onMessage: (res) => {
        const cleanRes = res
          .replace(/```json\s*|```$/g, "")
          .trim()
          .replace(/```/g, "")
          .trim();
        console.log("iiiiiiiiii555555", cleanRes);
        setMessageStr(cleanRes);
        setGlobalLoading(false);
      },
      onFinished: () => {},
    });
  };
  const changeLocale = () => {
    setMessageStr("");
  };
  // 报告生成-文件审查
  const startInspectFile = async () => {
    setMessageStr("");
    setMarkeDownId(generateRandom8Digits());
    const inputs = {
      type: "报告生成",
      // info: audioContentData,
      report_generation: "材料审查",
    };
    const files: any = [];
    uploadFileList.forEach((item: any) => {
      files.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    // let results = "";
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: agentId || "",
        agentId: agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs,
          // pageinfo: pageInfo,
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: "材料审查",
          files,
        },
      },
      query: {},
      onMessage: (res) => {
        // results += res;
        const cleanRes = res
          .replace(/```json\s*|```$/g, "")
          .trim()
          .replace(/```/g, "")
          .trim();
        console.log("uuuuuuuuuu", cleanRes);
        setMessageStr(cleanRes);
        setGlobalLoading(false);
      },
      onFinished: () => {},
    });

    // const res = await chatMessage(inputs, "材料审查", files);
  };
  // 报告生成-文件审查-下载
  const downloadInspectFiles = async () => {
    // const inputs = {
    //   type: "报告生成",
    //   // info: audioContentData,
    //   report_generation: "文件下载",
    //   wendangleixin: "资料审查",
    // };
    // const res = await chatMessage(inputs, "文件下载");
    // console.log("nnnnnnnn", res);
  };
  // 报告生成-贷前审查报告-下载
  const downloadFiles = async (id: string) => {
    console.log(id);
    if (id == "1") {
      // const inputs = {
      //   type: "报告生成",
      //   // info: audioContentData,
      //   report_generation: "文件下载",
      //   wendangleixin: "贷前审查报告",
      //   info: templateJson,
      //   query: messageStr,
      // };
      // const files: any = [];
      // uploadFileList.forEach((item: any) => {
      //   files.push({
      //     type: item.fileType || "document",
      //     transfer_method: "local_file",
      //     upload_file_id: item.id,
      //   });
      // });
      // const res = await chatMessage(inputs, messageStr, files);
      // console.log("rrrrrrrrrrrr", res);
      // const data = fileData;
      // const blob = new Blob([data]);
      const alink: any = document.createElement("a");
      alink.href = "/copilot/public/files/【输出成果】贷前审查报告.docx";
      alink.download = "贷前审查报告.docx";
      alink.click();
    } else if (id == "2") {
      const inputs = {
        type: "报告生成",
        // info: audioContentData,
        report_generation: "文件下载",
        wendangleixin: "会话总结",
      };
      const res: any = await chatMessage(inputs, messageStr);
      console.log("bbbbbbbbbb", res);
      const regex = /\[(.*?)\]\((.*?)\)/; // 正确匹配 [text](url) 并捕获 url
      const match = res.match(regex);

      if (match) {
        const url = match[2]; // 提取第二个捕获组（括号内的内容）
        const href = `https://copilot.sino-bridge.com${url}`;
        window.open(href);
      }
    } else if (id == "3") {
      const inputs = {
        type: "报告生成",
        // info: audioContentData,
        report_generation: "文件下载",
        wendangleixin: "语音质检",
      };
      const res: any = await chatMessage(inputs, messageStr);
      console.log("bbbbbbbbbb", res);
      const regex = /\[(.*?)\]\((.*?)\)/; // 正确匹配 [text](url) 并捕获 url
      const match = res.match(regex);

      if (match) {
        const url = match[2]; // 提取第二个捕获组（括号内的内容）
        const href = `https://copilot.sino-bridge.com${url}`;
        window.open(href);
      }
    }
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "企业画像",
      children: (
        <Enterpriseprofile
          companyInfo={companyInfo}
          financialInfo={financialInfo}
          companyTags={companyTags}
          clickHisTradModal={openHisTradModal}
          clickHisChatModal={openHisChatModalOpen}
        />
      ),
    },
    {
      key: "2",
      label: "产品推荐",
      children: (
        <ProductRecommend
          productList={productList}
          sendMessage={sendMessage}
          sendMessageAll={sendMessageAll}
        />
      ),
    },
    {
      key: "3",
      label: "文件生成",
      children: (
        <FileGenerate
          generateReports={generateReports}
          messageStr={messageStr}
          changeReportTypeValue={changeReportTypeValue}
          uploadFileList={uploadFileList}
          audioContentData={getAudioContentData}
          audioContent={audioContent}
          defaultMessage={defaultMessage}
          defaultMessageData={defaultMessageData}
          changeLocale={changeLocale}
          startInspectFile={startInspectFile}
          downloadInspectFiles={downloadInspectFiles}
          downloadFiles={downloadFiles}
          markeDownId={markeDownId}
        />
      ),
    },
  ];
  const changeHisTradeCurId = (id: string) => {
    setHisTradeCurId(id);
  };

  const hisTradList = [
    {
      id: "1",
      title: "研发中心建设贷",
      finish: false,
    },
    {
      id: "2",
      title: "科创设备融资",
      finish: true,
    },
  ];
  // 智能聊天-初始化
  const chatMessage = async (inputs: any, query?: string, files?: any) => {
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    return new Promise((resolve) => {
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId || "",
          agentId: agentId || "",
          path: "/chat-messages",
          difyJson: {
            inputs,
            // pageinfo: pageInfo,
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query,
            files,
          },
        },
        query: {},
        onMessage: () => {},
        onFinished: (resultData: any) => {
          resolve(resultData); // 将结果传出去
          setGlobalLoading(false);
        },
      });
    });
  };
  // 营销话术
  const sessionBtn = async () => {
    console.log("话术");
    const inputs = {
      type: "营销话术",
      info: JSON.stringify(messageList),
    };
    let results = "";
    const res = await chatMessage(inputs, "营销话术");
    console.log("ressjsjssk", res);
    results += res;
    // const cleanRes = results
    //   .replace(/```json\s*|```$/g, "")
    //   .trim()
    //   .replace(/```/g, "")
    //   .trim();
    const message = results.split("-");
    setMarketScript(message);
    // const message = JSON.parse(cleanRes);
  };
  // 企业画像-天眼查数据
  const getCompanyInfo = async () => {
    console.log("企业画像");
    const inputs = {
      type: "企业画像",
      Enterprise_portrait: "天眼查",
      company: "科大讯飞股份有限公司",
    };
    const res: any = await chatMessage(inputs, "1");
    const cleanRes = res
      .replace(/```json\s*|```$/g, "")
      .trim()
      .replace(/```/g, "")
      .trim()
      .replace(/'''json\s*|'''$/g, "")
      .trim()
      .replace(/'''/g, "")
      .trim();
    console.log("企业画像-天眼查333", cleanRes);
    const companyInfoData: any = JSON.parse(cleanRes)["[基本信息]"][0];
    const financialInfo: any = JSON.parse(cleanRes)["[2025第一季度]"][0];
    console.log(companyInfoData, "fffff");
    setCompanyInfo(companyInfoData);
    setFinancialInfo(financialInfo);
    getCompanyTags();
  };

  const getCompanyTags = async () => {
    const inputs = {
      type: "企业画像",
      Enterprise_portrait: "标签",
      company: "科大讯飞股份有限公司",
      info: "[大型企业，中型企业，小型企业，微型企业，上市公司瞪羚企业，独角兽企业，专精特新，高新科技产业，高研发投入，专利和技术，高净值用户，价格敏感用户，新一代信息技术、高端装备制造、新材料、生物医药、新能源汽车，人工智能]",
    };
    const res: any = await chatMessage(inputs, "1");
    console.log("企业画像标签res", res);
    // const tagsStr = res.split("</think>")[1];
    // console.log(tagsStr);
    const tags = res.split("，");
    setCompanyTags(tags);
  };
  // 随机生成8位数字
  const generateRandom8Digits = () => {
    // 生成一个0到99999999之间的随机数
    let randomNumber = Math.floor(Math.random() * *********).toString();

    // 将随机数转换为8位数字，前面不足8位的用0填充
    randomNumber = String(randomNumber).padStart(8, "0");

    return randomNumber;
  };
  // 产品推荐
  const getProductData = async (btnName: string) => {
    const inputs = {
      type: "产品推荐",
      info: JSON.stringify(messageList),
    };
    let results = "";
    const res: any = await chatMessage(inputs, btnName);
    results += res;
    const cleanRes = results
      .replace(/```json\s*|```$/g, "")
      .trim()
      .replace(/```/g, "")
      .trim();
    console.log("9999999999", cleanRes);
    const message = JSON.parse(cleanRes);
    // messageList.push({
    //   id: generateRandom8Digits(),
    //   msg: "智能产品推荐",
    //   msgBelong: "1",
    //   time: dayjs().format("HH:ss"),
    //   type: {
    //     type: "product",
    //     productList: message,
    //   },
    // });
    // setMessageList([...messageList]);
    setProductList(message);
    // }
  };
  const changeMarkeDownStatus = () => {};
  useEffect(() => {
    setMessageList([...getData("messageList")]);
    setUploadFileList([...getData("uploadFileList")]);
    setMessageListHis([...getData("messageListHis")]);
    getCompanyInfo();
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
  }, []);
  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Layout className="intelligent-service-layout">
        <Header className="header">
          <Flex justify="space-between" align="center">
            <Typography.Title
              level={5}
              style={{ marginBottom: 0, color: "#3c82f6" }}
            >
              智能客服中心
            </Typography.Title>
            <Space size="small" className="header-space">
              <Flex align="center" gap={20}>
                <BellFilled style={{ fontSize: 16 }} />
                <Flex align="center">
                  <Avatar size="small" icon={<UserOutlined />} />
                  <span style={{ marginLeft: 8 }}>小林</span>
                </Flex>
              </Flex>
            </Space>
          </Flex>
        </Header>
        <Layout className="content-layout">
          <Sider className="content-sider" width={300}>
            <Flex className="content-search">
              <Input
                className="search-ipt"
                placeholder="搜索客户/会话..."
                style={{ fontSize: "14px" }}
                prefix={<SearchOutlined />}
              />
            </Flex>
            <Flex vertical={true} gap={10} className="customer-list">
              {customerList &&
                customerList.length > 0 &&
                customerList.map((item: any) => (
                  <Flex
                    key={item.id}
                    className={
                      item.id == curCustomerId
                        ? "customer-list-item customer-list-active"
                        : "customer-list-item"
                    }
                    onClick={() => selectCustomer(item.id)}
                  >
                    <Flex className="list-item-icon">
                      {/* <WechatOutlined style={{ fontSize: "20px" }} /> */}
                      {/* <img style={{ width: "24px" }} src="#" alt="" /> */}
                      <Badge
                        count={item.msgNum}
                        size="small"
                        offset={[-5, 0]}
                        key={item.id}
                        className="customer-list-badge"
                      >
                        <Avatar
                          shape="square"
                          size="large"
                          icon={<UserOutlined />}
                        />
                      </Badge>
                    </Flex>
                    <Flex vertical={true} className="list-item-name">
                      <Flex justify="space-between">
                        <Flex>
                          <p>{item.name}</p>
                        </Flex>
                        <Flex>
                          <p className="desc">{item.time}</p>
                        </Flex>
                      </Flex>
                      <Flex className="item-name-desc">
                        <p className="desc">{item.desc}</p>
                      </Flex>
                    </Flex>
                    {/* <Flex>
                  <span></span>
                </Flex> */}
                  </Flex>
                ))}
            </Flex>
          </Sider>
          <Content className="content">
            <Session
              sessionRef={sessionRef}
              messageList={messageList}
              submitMessage={submitMsg}
              sessionBtnList={sessionBtnList}
              currentUser={currentUser}
              sessionBtn={sessionBtn}
              marketScript={marketScript}
              changeMarkeDownStatus={changeMarkeDownStatus}
              markeDowanStatus={[]}
            ></Session>
          </Content>
          <Sider
            className="content-sider content-sider-right"
            width={300}
            style={{ marginLeft: "15px" }}
          >
            <Tabs
              className="right-tabs"
              defaultActiveKey="1"
              items={items}
              onChange={onChange}
              // centered={true}
            />
          </Sider>
        </Layout>
      </Layout>
      {/* 历史交易 */}
      <Modal
        title={
          <Flex className="his-trade-modal">
            <Flex className="his-trade-modal-tit">交易历史记录</Flex>
            <Flex flex={1} justify="end" className="his-trade-modal-con">
              <Flex>
                <Input placeholder="搜索产品名称/交易编号" />
                <CloseOutlined
                  className="his-trade-modal-icon"
                  onClick={handleCancelHisTrande}
                />
              </Flex>
            </Flex>
          </Flex>
        }
        width={1200}
        closable={false}
        open={hisTradeModalOpen}
        // onOk={handleOk}
        onCancel={handleCancelHisTrande}
        maskClosable={false}
        footer
      >
        <Flex className="his-trade-content">
          <Flex className="his-trade-content-tags">
            {hisTradeTagsList.map((item) => (
              <Flex
                key={item.id}
                className={
                  hisTradeCurId == item.id
                    ? "his-trade-content-tags-item his-trade-content-tags-active"
                    : "his-trade-content-tags-item"
                }
                onClick={() => changeHisTradeCurId(item.id)}
              >
                {item.tagName}
              </Flex>
            ))}
          </Flex>
        </Flex>
        <Flex className="his-trade-content-info" vertical={true}>
          <Flex className="his-trade-content-card">
            <Row gutter={[20, 20]} style={{ width: "100%" }}>
              <Col className="gutter-row" span={6}>
                <Flex
                  className="his-trade-content-card-item"
                  style={{ background: "#EFF6FF" }}
                  vertical={true}
                  justify="start"
                >
                  <Flex className="his-trade-item-tit">累计交易金额</Flex>
                  <Flex className="his-trade-item-price">¥ 12,857,600</Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={6}>
                <Flex
                  className="his-trade-content-card-item"
                  style={{ background: "#EFFDF4" }}
                  vertical={true}
                  justify="start"
                >
                  <Flex className="his-trade-item-tit">当年交易笔数</Flex>
                  <Flex className="his-trade-item-price">2 笔</Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={6}>
                <Flex
                  className="his-trade-content-card-item"
                  style={{ background: "#FAF5FF" }}
                  vertical={true}
                  justify="start"
                >
                  <Flex className="his-trade-item-tit">最近交易时间</Flex>
                  <Flex className="his-trade-item-price">2024-01-15</Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={6}>
                <Flex
                  className="his-trade-content-card-item"
                  style={{ background: "#FEF2F2" }}
                  vertical={true}
                  justify="start"
                >
                  <Flex className="his-trade-item-tit">授信额度使用率</Flex>
                  <Flex className="his-trade-item-price">68.5%</Flex>
                </Flex>
              </Col>
            </Row>
          </Flex>
          {hisTradList.map((item) => (
            <Card key={item.id} className="his-trade-content-detail">
              <Flex className="his-trade-content-detail-item">
                <Flex
                  className={
                    item.finish
                      ? "his-trade-content-detail-logo his-trade-content-detail-logo-green"
                      : "his-trade-content-detail-logo"
                  }
                  style={{ background: "#EFFDF4" }}
                  justify="center"
                >
                  <BankFilled
                    className={
                      item.finish
                        ? "detail-logo logo-green"
                        : "detail-logo logo-blue"
                    }
                  />
                </Flex>
                <Flex
                  flex={1}
                  vertical={true}
                  className="his-trade-content-detail-content"
                >
                  <Flex flex={1} className="detail-content-fir">
                    <Flex>{item.title}</Flex>
                    <Flex flex={1} justify="end">
                      ¥2,500,000
                    </Flex>
                  </Flex>
                  <Flex flex={1} className="detail-content-desc">
                    <Flex>固定资产贷款</Flex>
                    <Flex flex={1} justify="end">
                      {!item.finish ? (
                        <Tag
                          color="processing"
                          bordered={false}
                          style={{ marginRight: 0 }}
                        >
                          高净值用户
                        </Tag>
                      ) : (
                        <Tag
                          color="success"
                          bordered={false}
                          style={{ marginRight: 0 }}
                        >
                          已完成
                        </Tag>
                      )}
                    </Flex>
                  </Flex>
                  <Flex flex={1} className="detail-content-data">
                    <Flex>申请日期: 2024-01-15</Flex>
                  </Flex>
                  {!item.finish && (
                    <Flex flex={1} className="detail-content-step">
                      <Flex>当前阶段：贷前调查和业务审批</Flex>
                      <Flex flex={1} justify="end">
                        2/4
                      </Flex>
                    </Flex>
                  )}
                  <Flex
                    flex={1}
                    className="his-trade-content-detail-steps"
                    gap={5}
                  >
                    <Flex
                      className={
                        item.finish
                          ? "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-green"
                          : "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-finish"
                      }
                      flex={1}
                    >
                      {!item.finish ? (
                        <span className="his-trade-content-detail-steps-name">
                          提交申请
                        </span>
                      ) : (
                        <span></span>
                      )}
                    </Flex>
                    <Flex
                      className={
                        item.finish
                          ? "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-green"
                          : "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-finish"
                      }
                      flex={1}
                    >
                      {!item.finish ? (
                        <span className="his-trade-content-detail-steps-name">
                          贷前调查
                        </span>
                      ) : (
                        <span></span>
                      )}
                    </Flex>
                    <Flex
                      className={
                        item.finish
                          ? "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-green"
                          : "his-trade-content-detail-steps-item"
                      }
                      flex={1}
                    >
                      {!item.finish ? (
                        <span className="his-trade-content-detail-steps-name">
                          签订合同
                        </span>
                      ) : (
                        <span></span>
                      )}
                    </Flex>
                    <Flex
                      className={
                        item.finish
                          ? "his-trade-content-detail-steps-item his-trade-content-detail-steps-item-green"
                          : "his-trade-content-detail-steps-item"
                      }
                      flex={1}
                    >
                      {!item.finish ? (
                        <span className="his-trade-content-detail-steps-name">
                          贷款放款
                        </span>
                      ) : (
                        <span></span>
                      )}
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
            </Card>
          ))}
        </Flex>
        <Flex flex={1} style={{ padding: "0 20px" }}>
          <Flex>共2条记录</Flex>
          <Flex flex={1} justify="end">
            <Pagination defaultCurrent={1} total={2} />
          </Flex>
        </Flex>
      </Modal>
      {/* 历史聊天 */}
      <Modal
        title={
          <Flex className="his-trade-modal">
            <Flex className="his-trade-modal-tit">历史聊天记录</Flex>
            <Flex flex={1} justify="end" className="his-trade-modal-con">
              <Flex>
                <CloseOutlined
                  className="his-trade-modal-icon"
                  onClick={handleCancelHisChat}
                />
              </Flex>
            </Flex>
          </Flex>
        }
        width={1200}
        closable={false}
        open={hisChatModalOpen}
        // onOk={handleOk}
        onCancel={handleCancelHisChat}
        maskClosable={false}
        footer
      >
        <Flex flex={1} className="his-chat-modal" vertical={true}>
          <Flex flex={1} className="his-chat-modal-search">
            <Input placeholder="搜索聊天内容" />
          </Flex>
          <Flex className="his-chat-modal-select">
            <Space>
              <Flex>
                <DatePicker />
              </Flex>
              <Flex>
                <Select
                  defaultValue="all"
                  style={{ width: 130 }}
                  options={[
                    { value: "all", label: "全部客服类型" },
                    { value: "ai", label: "AI" },
                    { value: "person", label: "人工" },
                  ]}
                />
              </Flex>
              <Flex>
                <Select
                  defaultValue="all"
                  style={{ width: 130 }}
                  options={[{ value: "all", label: "全部记录" }]}
                />
              </Flex>
            </Space>
          </Flex>
          <Flex className="his-chat-modal-chat">
            <Flex className="session-content-message" vertical={true}>
              {messageListHis.map((item: Message) => (
                <Flex key={item.id}>
                  {item.msgBelong !== currentUser && (
                    <Flex className="message-item">
                      <Avatar shape="square" icon={<UserOutlined />} />
                      <Flex className="message-desc" vertical={true}>
                        <Flex style={{ whiteSpace: "break-spaces" }}>
                          {item.msg}
                        </Flex>
                        {item.type?.type == "button" && (
                          <Flex className="message-business-btn">
                            <Space>
                              {item.type.list &&
                                item.type.list.map((btn, index) => (
                                  <Button
                                    key={index}
                                    size="small"
                                    // onClick={() => clickMessageBtn(btn)}
                                  >
                                    {btn}
                                  </Button>
                                ))}
                            </Space>
                          </Flex>
                        )}
                        {item.type?.type == "upload" && (
                          <Flex className="message-upload">
                            <Upload.Dragger
                              showUploadList={false}
                              multiple={true}
                              // beforeUpload={beforeUpload}
                              accept=".docx,.doc,.pdf,.txt,.md"
                              // fileList={uploadedFiles}
                            >
                              <div className="ant-upload-drag-icon">
                                {/* {uploadedFiles.length > 0 ? (
                                  <CheckCircleFilled />
                                ) : (
                                  <InboxOutlined />
                                )} */}
                              </div>
                              <p className="ant-upload-hint">
                                <span>点击或将文件拖到此处上传</span>
                                <span>支持doc,docx,txt,pdf格式文件</span>
                              </p>
                            </Upload.Dragger>
                          </Flex>
                        )}
                        {item.type?.type == "form" && (
                          <Flex className="message-form" vertical={true}>
                            <Flex
                              className="message-form-title"
                              flex={1}
                              justify="center"
                            >
                              企业贷款业务申请表
                            </Flex>
                            <Flex className="message-form-type">
                              请您选择需要办理的业务类型:
                            </Flex>
                            <Flex className="message-form-type-btns">
                              <Space>
                                {businessTypeList.map(
                                  (item: any, index: number) => (
                                    <Flex
                                      key={item.id}
                                      // className="message-form-btn-item message-form-btn-item-active"
                                      className={
                                        index == 0
                                          ? "message-form-btn-item message-form-btn-item-active"
                                          : "message-form-btn-item"
                                      }
                                      // onClick={() => changeBusinessType(item.id)}
                                    >
                                      {item.name}
                                    </Flex>
                                  )
                                )}
                              </Space>
                            </Flex>
                            <Flex className="message-form-type">
                              企业基本信息:
                            </Flex>
                            <Flex className="message-form-info">
                              <Form
                                // name="basic"
                                // labelCol={{ span: 8 }}
                                // wrapperCol={{ span: 16 }}
                                style={{ width: "100%" }}
                                initialValues={{ remember: true }}
                                // onFinish={onFinish}
                                // onFinishFailed={onFinishFailed}
                                autoComplete="off"
                              >
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业名称"
                                        name="companyName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业名称",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业名称" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="统一社会信用代码"
                                        name="uscc"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入统一社会信用代码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入统一社会信用代码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业法定代表人姓名"
                                        name="legalName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业法定代表人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业法定代表人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业联系电话"
                                        name="companyNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业联系电话",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业联系电话" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业注册地址"
                                        name="address"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业注册地址",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业注册地址" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业成立日期"
                                        name="incorporationDate"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择企业成立日期",
                                          },
                                        ]}
                                      >
                                        <DatePicker style={{ width: "100%" }} />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex className="message-form-type">
                                  贷款信息
                                </Flex>
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="申请金额(万元)"
                                        name="amount"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入申请金额",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入申请金额" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="期望贷款期限"
                                        name="loanTerm"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择期望贷款期限",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择期望贷款期限"
                                          options={[
                                            { value: "six", label: "6个月" },
                                            { value: "oneYear", label: "1年" },
                                            {
                                              value: "twoYear",
                                              label: "2年",
                                            },
                                            {
                                              value: "threeYear",
                                              label: "3年",
                                            },
                                          ]}
                                          // onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="还款方式"
                                        name="repaymentMethod"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择还款方式",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择还款方式"
                                          options={[
                                            { value: "1", label: "等额本金" },
                                            { value: "2", label: "等额本息" },
                                          ]}
                                          // onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  style={{ width: "100%", marginBottom: 20 }}
                                  vertical={true}
                                >
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={4}>
                                      <Form.Item
                                        label="自营贷款类型"
                                        name="selfLoanTypes"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入自营贷款类型",
                                          },
                                        ]}
                                      >
                                        <Radio.Group
                                          // value={loanType}
                                          // onChange={changeLoanType}
                                          style={{
                                            display: "flex",
                                            flexDirection: "column",
                                            gap: 8,
                                          }}
                                          options={[
                                            {
                                              value: 1,
                                              label:
                                                "固定资产贷款（如厂房、设备购置）",
                                            },
                                            {
                                              value: 2,
                                              label:
                                                "流动资产贷款（如原材料采购、支付账款）",
                                            },
                                          ]}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={3}>
                                      <Form.Item
                                        label="贷款用途说明"
                                        name="loanPurpose"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入贷款用途说明",
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          placeholder="例如：用于购买一个新的生产线"
                                          rows={2}
                                          autoSize={{ minRows: 4, maxRows: 4 }}
                                        ></Input.TextArea>
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  className="message-form-type"
                                  style={{
                                    marginTop: 65,
                                  }}
                                >
                                  联系人信息
                                </Flex>{" "}
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人姓名"
                                        name="contactName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人手机号码"
                                        name="contactNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人手机号码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人手机号码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人电子邮箱"
                                        name="contactEmail"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人电子邮箱",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人电子邮箱" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人职务"
                                        name="contactPosition"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人职务",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人职务" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                {/* <Flex flex={1}>
                                  <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    onClick={submitFormData}
                                  >
                                    提交
                                  </Button>
                                </Flex> */}
                              </Form>
                            </Flex>
                          </Flex>
                        )}
                        {item.type?.type == "product" && (
                          <Flex className="message-product" wrap>
                            {item.type.productList.length > 0 &&
                              item.type.productList.map(
                                (item: any, index: number) => (
                                  <Flex
                                    key={index}
                                    className="message-product-item"
                                    flex={"30%"}
                                    vertical={true}
                                  >
                                    <Flex className="message-product-item-img">
                                      <img
                                        style={{
                                          width: "100%",
                                          height: "100%",
                                          borderRadius: 5,
                                        }}
                                        src={dai}
                                      />
                                    </Flex>
                                    <Flex className="message-product-item-name">
                                      <Flex className="message-product-item-title">
                                        {item.productName}
                                      </Flex>
                                      {/* <Flex flex={1} justify="end">
                                        <Tag
                                          color="blue"
                                          bordered={false}
                                          style={{
                                            marginRight: 0,
                                            fontSize: 12,
                                          }}
                                        >
                                          {item.type}
                                        </Tag>
                                      </Flex> */}
                                    </Flex>
                                    <Flex className="message-product-item-desc">
                                      {item.productDescription}
                                    </Flex>
                                    <Flex
                                      className="message-product-item-card"
                                      gap={10}
                                    >
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        justify="center"
                                        align="center"
                                        flex={1}
                                      >
                                        <Flex>
                                          <AreaChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最高额度
                                        </Flex>
                                        <Flex
                                          className="message-product-card-item-price"
                                          justify="center"
                                          style={{ textAlign: "center" }}
                                        >
                                          {item.coreElements.quota}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <PieChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最长期限
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.term}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <BarChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          担保方式
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.guarantee}
                                        </Flex>
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-hot"
                                      vertical={true}
                                    >
                                      <Flex className="message-product-hot-name">
                                        产品亮点
                                      </Flex>
                                      <Flex
                                        className="message-product-hot-list"
                                        vertical={true}
                                        justify="start"
                                      >
                                        {Array.isArray(
                                          item.productHighlights
                                        ) &&
                                          item.productHighlights.map(
                                            (item: string, index: number) => (
                                              <Flex
                                                key={index}
                                                align="center"
                                                className="message-product-hot-list-item"
                                              >
                                                <Flex>
                                                  <CheckCircleOutlined
                                                    style={{
                                                      background: "#2B7FFE",
                                                      borderRadius: "50%",
                                                      color: "#fff",
                                                      fontSize: 15,
                                                      marginRight: 5,
                                                      height: 15,
                                                    }}
                                                  />
                                                </Flex>
                                                <Flex className="message-product-hot-list-desc">
                                                  {item}
                                                </Flex>
                                              </Flex>
                                            )
                                          )}
                                        {typeof item.productHighlights ==
                                          "string" && (
                                          <Flex
                                            key={index}
                                            align="center"
                                            className="message-product-hot-list-item"
                                          >
                                            <Flex>
                                              <CheckCircleOutlined
                                                style={{
                                                  background: "#2B7FFE",
                                                  borderRadius: "50%",
                                                  color: "#fff",
                                                  fontSize: 15,
                                                  marginRight: 5,
                                                  height: 15,
                                                }}
                                              />
                                            </Flex>
                                            <Flex
                                              className="message-product-hot-list-desc"
                                              flex={1}
                                            >
                                              {item.productHighlights}
                                            </Flex>
                                          </Flex>
                                        )}
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-btn"
                                      flex={1}
                                      justify="end"
                                    >
                                      <Button
                                        type="primary"
                                        size="small"
                                        style={{ marginTop: "auto" }}
                                      >
                                        立即申请
                                      </Button>
                                    </Flex>
                                  </Flex>
                                )
                              )}
                          </Flex>
                        )}
                      </Flex>
                    </Flex>
                  )}
                  {item.msgBelong == currentUser && (
                    <Flex className="message-item message-item-right">
                      <Flex className="message-desc" vertical={true}>
                        <Flex style={{ marginBottom: 10 }}>
                          <Tag color="#6466F1">AI</Tag>
                        </Flex>
                        <Flex>{item.msg}</Flex>
                        {item.type?.type == "button" && (
                          <Flex className="message-business-btn">
                            <Space>
                              {item.type.list &&
                                item.type.list.map((btn, index) => (
                                  <Button
                                    key={index}
                                    size="small"
                                    // onClick={() => clickMessageBtn(btn)}
                                  >
                                    {btn}
                                  </Button>
                                ))}
                            </Space>
                          </Flex>
                        )}
                        {item.type?.type == "upload" && (
                          <Flex className="message-upload">
                            <Upload.Dragger
                              showUploadList={false}
                              multiple={true}
                              // beforeUpload={beforeUpload}
                              accept=".docx,.doc,.pdf,.txt,.md"
                              // fileList={uploadedFiles}
                            >
                              <div className="ant-upload-drag-icon">
                                {/* {uploadedFiles.length > 0 ? (
                                  <CheckCircleFilled />
                                ) : (
                                  <InboxOutlined />
                                )} */}
                              </div>
                              <p className="ant-upload-hint">
                                <span>点击或将文件拖到此处上传</span>
                                <span>支持doc,docx,txt,pdf格式文件</span>
                              </p>
                            </Upload.Dragger>
                          </Flex>
                        )}
                        {item.type?.type == "form" && (
                          <Flex className="message-form" vertical={true}>
                            <Flex
                              className="message-form-title"
                              flex={1}
                              justify="center"
                            >
                              企业贷款业务申请表
                            </Flex>
                            <Flex className="message-form-type">
                              请您选择需要办理的业务类型:
                            </Flex>
                            <Flex className="message-form-type-btns">
                              <Space>
                                {businessTypeList.map(
                                  (item: any, index: number) => (
                                    <Flex
                                      key={item.id}
                                      // className="message-form-btn-item message-form-btn-item-active"
                                      className={
                                        index == 0
                                          ? "message-form-btn-item message-form-btn-item-active"
                                          : "message-form-btn-item"
                                      }
                                      // onClick={() => changeBusinessType(item.id)}
                                    >
                                      {item.name}
                                    </Flex>
                                  )
                                )}
                              </Space>
                            </Flex>
                            <Flex className="message-form-type">
                              企业基本信息:
                            </Flex>
                            <Flex className="message-form-info">
                              <Form
                                // name="basic"
                                // labelCol={{ span: 8 }}
                                // wrapperCol={{ span: 16 }}
                                style={{ width: "100%" }}
                                initialValues={{ remember: true }}
                                // onFinish={onFinish}
                                // onFinishFailed={onFinishFailed}
                                autoComplete="off"
                              >
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业名称"
                                        name="companyName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业名称",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业名称" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="统一社会信用代码"
                                        name="uscc"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入统一社会信用代码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入统一社会信用代码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业法定代表人姓名"
                                        name="legalName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业法定代表人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业法定代表人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业联系电话"
                                        name="companyNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业联系电话",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业联系电话" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业注册地址"
                                        name="address"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业注册地址",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业注册地址" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业成立日期"
                                        name="incorporationDate"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择企业成立日期",
                                          },
                                        ]}
                                      >
                                        <DatePicker style={{ width: "100%" }} />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex className="message-form-type">
                                  贷款信息
                                </Flex>
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="申请金额(万元)"
                                        name="amount"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入申请金额",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入申请金额" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="期望贷款期限"
                                        name="loanTerm"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择期望贷款期限",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择期望贷款期限"
                                          options={[
                                            { value: "six", label: "6个月" },
                                            { value: "oneYear", label: "1年" },
                                            {
                                              value: "twoYear",
                                              label: "2年",
                                            },
                                            {
                                              value: "threeYear",
                                              label: "3年",
                                            },
                                          ]}
                                          // onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="还款方式"
                                        name="repaymentMethod"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择还款方式",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择还款方式"
                                          options={[
                                            { value: "1", label: "等额本金" },
                                            { value: "2", label: "等额本息" },
                                          ]}
                                          // onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  style={{ width: "100%", marginBottom: 20 }}
                                  vertical={true}
                                >
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={4}>
                                      <Form.Item
                                        label="自营贷款类型"
                                        name="selfLoanTypes"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入自营贷款类型",
                                          },
                                        ]}
                                      >
                                        <Radio.Group
                                          // value={loanType}
                                          // onChange={changeLoanType}
                                          style={{
                                            display: "flex",
                                            flexDirection: "column",
                                            gap: 8,
                                          }}
                                          options={[
                                            {
                                              value: 1,
                                              label:
                                                "固定资产贷款（如厂房、设备购置）",
                                            },
                                            {
                                              value: 2,
                                              label:
                                                "流动资产贷款（如原材料采购、支付账款）",
                                            },
                                          ]}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={3}>
                                      <Form.Item
                                        label="贷款用途说明"
                                        name="loanPurpose"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入贷款用途说明",
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          placeholder="例如：用于购买一个新的生产线"
                                          rows={2}
                                          autoSize={{ minRows: 4, maxRows: 4 }}
                                        ></Input.TextArea>
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  className="message-form-type"
                                  style={{
                                    marginTop: 65,
                                  }}
                                >
                                  联系人信息
                                </Flex>{" "}
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人姓名"
                                        name="contactName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人手机号码"
                                        name="contactNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人手机号码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人手机号码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人电子邮箱"
                                        name="contactEmail"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人电子邮箱",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人电子邮箱" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人职务"
                                        name="contactPosition"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人职务",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人职务" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                {/* <Flex flex={1}>
                                  <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    onClick={submitFormData}
                                  >
                                    提交
                                  </Button>
                                </Flex> */}
                              </Form>
                            </Flex>
                          </Flex>
                        )}
                        {item.type?.type == "product" && (
                          <Flex className="message-product" wrap>
                            {item.type.productList.length > 0 &&
                              item.type.productList.map(
                                (item: any, index: number) => (
                                  <Flex
                                    key={index}
                                    className="message-product-item"
                                    flex={"30%"}
                                    vertical={true}
                                  >
                                    <Flex className="message-product-item-img">
                                      <img
                                        style={{
                                          width: "100%",
                                          height: "100%",
                                          borderRadius: 5,
                                        }}
                                        src={dai}
                                      />
                                    </Flex>
                                    <Flex className="message-product-item-name">
                                      <Flex className="message-product-item-title">
                                        {item.productName}
                                      </Flex>
                                      {/* <Flex flex={1} justify="end">
                                        <Tag
                                          color="blue"
                                          bordered={false}
                                          style={{
                                            marginRight: 0,
                                            fontSize: 12,
                                          }}
                                        >
                                          {item.type}
                                        </Tag>
                                      </Flex> */}
                                    </Flex>
                                    <Flex className="message-product-item-desc">
                                      {item.productDescription}
                                    </Flex>
                                    <Flex
                                      className="message-product-item-card"
                                      gap={10}
                                    >
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        justify="center"
                                        align="center"
                                        flex={1}
                                      >
                                        <Flex>
                                          <AreaChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最高额度
                                        </Flex>
                                        <Flex
                                          className="message-product-card-item-price"
                                          justify="center"
                                          style={{ textAlign: "center" }}
                                        >
                                          {item.coreElements.quota}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <PieChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最长期限
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.term}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <BarChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          担保方式
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.guarantee}
                                        </Flex>
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-hot"
                                      vertical={true}
                                    >
                                      <Flex className="message-product-hot-name">
                                        产品亮点
                                      </Flex>
                                      <Flex
                                        className="message-product-hot-list"
                                        vertical={true}
                                        justify="start"
                                      >
                                        {Array.isArray(
                                          item.productHighlights
                                        ) &&
                                          item.productHighlights.map(
                                            (item: string, index: number) => (
                                              <Flex
                                                key={index}
                                                align="center"
                                                className="message-product-hot-list-item"
                                              >
                                                <Flex>
                                                  <CheckCircleOutlined
                                                    style={{
                                                      background: "#2B7FFE",
                                                      borderRadius: "50%",
                                                      color: "#fff",
                                                      fontSize: 15,
                                                      marginRight: 5,
                                                      height: 15,
                                                    }}
                                                  />
                                                </Flex>
                                                <Flex className="message-product-hot-list-desc">
                                                  {item}
                                                </Flex>
                                              </Flex>
                                            )
                                          )}
                                        {typeof item.productHighlights ==
                                          "string" && (
                                          <Flex
                                            key={index}
                                            align="center"
                                            className="message-product-hot-list-item"
                                          >
                                            <Flex>
                                              <CheckCircleOutlined
                                                style={{
                                                  background: "#2B7FFE",
                                                  borderRadius: "50%",
                                                  color: "#fff",
                                                  fontSize: 15,
                                                  marginRight: 5,
                                                  height: 15,
                                                }}
                                              />
                                            </Flex>
                                            <Flex
                                              className="message-product-hot-list-desc"
                                              flex={1}
                                            >
                                              {item.productHighlights}
                                            </Flex>
                                          </Flex>
                                        )}
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-btn"
                                      flex={1}
                                      justify="end"
                                    >
                                      <Button
                                        type="primary"
                                        size="small"
                                        style={{ marginTop: "auto" }}
                                      >
                                        立即申请
                                      </Button>
                                    </Flex>
                                  </Flex>
                                )
                              )}
                          </Flex>
                        )}
                      </Flex>
                      <Avatar shape="square" icon={<UserOutlined />} />
                    </Flex>
                  )}
                </Flex>
              ))}
            </Flex>
          </Flex>
          <Flex flex={1}>
            <Flex>共40条记录</Flex>
            <Flex flex={1} justify="end">
              <Pagination defaultCurrent={1} total={20} />
            </Flex>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
};
export default IntelligentService;
