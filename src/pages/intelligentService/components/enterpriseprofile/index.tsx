import React from "react";
import { Flex, Avatar, Tag, Card, Row, Col, Button } from "antd";
import "./index.less";
import { UserOutlined } from "@ant-design/icons";
import IconFont from "@/component/IconFont";
const Enterpriseprofile: React.FC<any> = ({
  clickHisTradModal,
  clickHisChatModal,
  companyTags,
  companyInfo,
  financialInfo,
}: any) => {
  const hisTradModal = () => {
    clickHisTradModal(true);
  };
  const hisChatModal = () => {
    clickHisChatModal(true);
  };
  return (
    <>
      <Flex className="enterprise-profile" vertical={true}>
        <Flex className="enterprise-info" vertical={true}>
          <Flex gap={10} style={{ marginBottom: 10 }}>
            <Avatar shape="square" size="large" icon={<UserOutlined />} />
            <Flex vertical={true} className="enterprise-text">
              <span className="enterprise-name">{companyInfo["企业名称"]}</span>
              <span className="enterprise-tab">科技服务</span>
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">注册资本</Flex>
            <Flex flex={1} justify="end">
              {companyInfo["注册资本"]}
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">成立时间</Flex>
            <Flex flex={1} justify="end">
              {companyInfo["成立时间"]}
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">企业规模</Flex>
            <Flex flex={1} justify="end">
              中型企业
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">所属行业</Flex>
            <Flex flex={1} justify="end">
              {companyInfo["所属行业"]}
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">所在地区</Flex>
            <Flex flex={1} justify="end">
              {companyInfo["注册地址"]}
            </Flex>
          </Flex>
          <Flex className="enterprise-desc">
            <Flex className="desc-title">偏好</Flex>
            <Flex flex={1} justify="end" wrap gap={5}>
              {companyTags.map((item: string, index: number) => (
                <Tag key={index} color="default">
                  {item}
                </Tag>
              ))}
            </Flex>
          </Flex>
        </Flex>
        <Flex className="financial-indicators" gap={10} vertical={true}>
          <Card className="financial-cards">
            <Flex className="card-title" align="center">
              <Flex className="title">财务指标</Flex>
              <Flex className="card-subtitle" flex={1} justify="end">
                点击查看明细
              </Flex>
            </Flex>
            <Row gutter={[10, 10]}>
              <Col className="gutter-row" span={12}>
                <Flex
                  vertical={true}
                  align="center"
                  className="financial-item"
                  style={{ background: "#EFF6FF" }}
                >
                  <Flex className="financial-item-icon">
                    <IconFont type="asset" />
                  </Flex>
                  <Flex className="financial-item-text">营业收入</Flex>
                  <Flex className="financial-item-text financial-item-desc">
                    {financialInfo["营业收入"]}
                  </Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={12}>
                <Flex
                  vertical={true}
                  align="center"
                  className="financial-item"
                  style={{ background: "#EFFDF4" }}
                >
                  <Flex className="financial-item-icon">
                    <IconFont type="rise" fill="#12A348" />
                  </Flex>
                  <Flex className="financial-item-text">净利润</Flex>
                  <Flex className="financial-item-text financial-item-desc">
                    {financialInfo["净利润"]}
                  </Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={12}>
                <Flex
                  vertical={true}
                  align="center"
                  className="financial-item"
                  style={{ background: "#FAF5FF" }}
                >
                  <Flex className="financial-item-icon">
                    <IconFont type="property" fill="#12A348" />
                  </Flex>
                  <Flex className="financial-item-text">总资产</Flex>
                  <Flex className="financial-item-text financial-item-desc">
                    {financialInfo["总资产"]}
                  </Flex>
                </Flex>
              </Col>
              <Col className="gutter-row" span={12}>
                <Flex
                  vertical={true}
                  align="center"
                  className="financial-item"
                  style={{ background: "#FEF2F2" }}
                >
                  <Flex className="financial-item-icon">
                    <IconFont type="warn" fill="#12A348" />
                  </Flex>
                  <Flex className="financial-item-text">总负债</Flex>
                  <Flex className="financial-item-text financial-item-desc">
                    {financialInfo["总负债"]}
                  </Flex>
                </Flex>
              </Col>
            </Row>
            <Flex className="financial-info">
              <Flex>净资产</Flex>
              <Flex className="financial-info-num" flex={1} justify="end">
                {financialInfo["净资产"]}
              </Flex>
            </Flex>
            <Flex className="financial-info">
              <Flex>净利润率</Flex>
              <Flex className="financial-info-num" flex={1} justify="end">
                {financialInfo["净利润率"]}
              </Flex>
            </Flex>
            <Flex className="financial-info">
              <Flex>净负债率</Flex>
              <Flex className="financial-info-num" flex={1} justify="end">
                {financialInfo["资产负债率"]}
              </Flex>
            </Flex>
          </Card>
          <Card className="history-cooperate">
            <Flex className="card-title" align="center">
              <Flex className="title">合作历史</Flex>
              <Flex className="card-subtitle" flex={1} justify="end">
                点击查看明细
              </Flex>
            </Flex>
            <Flex className="history-cooperate-desc">近期业务往来记录</Flex>
            <Flex className="history-cooperate-list" vertical={true} gap={10}>
              <Flex className="history-cooperate-item">
                <Flex className="cooperate-item-icon">
                  <Flex className="icon-bg" justify="center" align="center">
                    <IconFont type="historyBlue" style={{ fontSize: 14 }} />
                  </Flex>
                </Flex>
                <Flex vertical={true} flex={1} className="cooperate-item-text">
                  <Flex>
                    <Flex>研发中心建设贷</Flex>
                    <Flex
                      flex={1}
                      justify="end"
                      className="cooperate-item-desc"
                    >
                      ¥2,500,000
                    </Flex>
                  </Flex>
                  <Flex>
                    <Flex className="cooperate-item-desc">2024-01-17</Flex>
                    <Flex
                      flex={1}
                      justify="end"
                      className="cooperate-item-desc"
                    >
                      已完成
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
              <Flex className="history-cooperate-item">
                <Flex className="cooperate-item-icon">
                  <Flex className="icon-bg" justify="center" align="center">
                    <IconFont type="historyBlue" style={{ fontSize: 14 }} />
                  </Flex>
                </Flex>
                <Flex vertical={true} flex={1} className="cooperate-item-text">
                  <Flex>
                    <Flex>科创设备融资</Flex>
                    <Flex
                      flex={1}
                      justify="end"
                      className="cooperate-item-desc"
                    >
                      ¥1,800,000
                    </Flex>
                  </Flex>
                  <Flex>
                    <Flex className="cooperate-item-desc">2024-01-17</Flex>
                    <Flex
                      flex={1}
                      justify="end"
                      className="cooperate-item-desc"
                    >
                      已完成
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </Card>
        </Flex>
      </Flex>
      <Flex vertical={true} gap={10} className="enterprise-profile-footer">
        <Button onClick={hisChatModal}>查看对话历史</Button>
        <Button onClick={hisTradModal}>查看交易历史</Button>
      </Flex>
    </>
  );
};
export default Enterpriseprofile;
