.enterprise-profile {
  //   padding: 10px;
  height: calc(100vh - 292px);
  overflow-y: scroll;
  .enterprise-info {
    margin-bottom: 10px;
    padding: 10px;
    // min-height: 252px;
    border-radius: 10px;
    background-color: rgba(249, 250, 251, 1);
    .enterprise-text {
      font-size: 14px;
      color: rgba(79, 79, 79, 1);
      .enterprise-tab {
        font-size: 12px;
        color: rgba(154, 154, 154, 1);
      }
    }
    .enterprise-desc {
      margin-bottom: 8px;
      font-size: 14px;
      color: rgba(79, 79, 79, 1);
      .desc-title {
        color: rgba(154, 154, 154, 1);
      }
    }
  }
  .financial-indicators {
    .financial-cards {
      padding: 10px;
      width: 100%;
      background-color: #fff;
      .card-title {
        margin-bottom: 10px;
        .title {
          font-weight: 500;
        }
      }
      .financial-info {
        padding-top: 10px;
        font-size: 10px;
        .financial-info-num {
          font-weight: 500;
          color: #000;
        }
      }
      .ant-card-body {
        padding: 0;
      }
      .financial-item {
        padding: 8px;
        border-radius: 5px;
        .financial-item-text {
          font-size: 10px;
          color: rgba(154, 154, 154, 1);
        }
        .financial-item-desc {
          font-weight: 500;
          color: rgba(79, 79, 79, 1);
        }
        .financial-item-icon {
          margin-bottom: 5px;
        }
      }
    }
    .history-cooperate {
      padding: 10px;
      width: 100%;
      background-color: #fff;
      .card-title {
        // margin-bottom: 4px;
        .title {
          font-weight: 500;
        }
      }
      .history-cooperate-desc {
        margin-bottom: 10px;
        font-size: 10px;
      }
      .ant-card-body {
        padding: 0;
      }
      .history-cooperate-list {
        .history-cooperate-item {
          padding: 10px;
          background: #f9fafb;
          border-radius: 5px;
          .cooperate-item-icon {
            margin-right: 10px;
            .icon-bg {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background: #dbeafe;
            }
          }
          .cooperate-item-text {
            font-size: 12px;
            font-weight: 500;
            .cooperate-item-desc {
              font-size: 10px;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
}
.card-subtitle {
  font-size: 12px;
  cursor: pointer;
  color: #0f40f5;
}
.enterprise-profile-footer {
  padding-top: 10px;
}
