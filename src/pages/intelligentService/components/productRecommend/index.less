.product-recommend {
  //   .product-title {
  //   }

  .product-text {
    margin-bottom: var(--ant-margin-xs);
    font-size: var(--ant-font-size-sm);
  }
  .product-card {
    margin-bottom: 10px;
    padding: 10px;
    .product-card-img {
      .product-card-img-box {
        border-radius: var(--ant-border-radius-sm);
        img {
          border-radius: var(--ant-border-radius-sm);
        }
      }
      .product-card-img-title {
        margin-top: var(--ant-margin-sm);
        .img-title {
          margin-bottom: var(--ant-margin-sm);
          font-size: var(--ant-font-size);
          font-weight: var(--ant-font-weight-strong);
        }
        .star-num {
          margin-left: var(--ant-margin-xxs);
          font-size: var(--ant-font-size-sm);
          font-weight: var(--ant-font-weight-strong);
        }
        .product-card-img-title-tag {
          margin-right: 0px;
          font-size: 10px;
        }
      }
    }
    .product-card-desc {
      margin-bottom: var(--ant-margin-xs);
      font-size: var(--ant-font-size-sm);
    }
    .product-card-footer {
      .product-card-footer-price {
        font-size: var(--ant-font-size);
        font-weight: var(--ant-font-weight-strong);
        color: var(--ant-color-success-text);
      }
      .product-card-footer-btn {
        font-size: 10px;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
}

.product-more {
  padding-top: 12px;
  .more-btn {
    width: 100%;
    font-size: var(--ant-font-size-sm);
  }
}

.product-card-list {
  height: calc(100vh - 312px);
  overflow-y: scroll;
}
