import React from "react";
import "./index.less";
import { Flex, Typography, Card, Tag, Button, Space } from "antd";
import dai from "@/assets/images/customer/dai1.png";
import dai2 from "@/assets/images/customer/dai2.png";
import dai3 from "@/assets/images/customer/dia3.png";
const ProductRecommend: React.FC<any> = ({
  productList,
  sendMessage,
  sendMessageAll,
}: any) => {
  const sendOneMessage = (item: any) => {
    sendMessage(item);
  };
  const sendAllMessage = (item: any) => {
    sendMessageAll(item);
  };
  return (
    <>
      <Flex className="product-recommend" vertical={true}>
        <Typography.Title className="product-title" level={5}>
          智能产品推荐
        </Typography.Title>
        <Typography.Text className="product-text">
          基于AI算法为您推荐最合适的产品
        </Typography.Text>
        <Flex className="product-card-list" vertical={true}>
          {productList.map((item: any, index: number) => (
            <Card key={index} className="product-card">
              <Flex vertical={true} className="product-card-img">
                <Flex className="product-card-img-box">
                  <img
                    src={index == 0 ? dai : index == 1 ? dai2 : dai3}
                    alt=""
                    style={{ width: "100%", height: "120px" }}
                  />
                </Flex>
                <Flex flex={1} className="product-card-img-title">
                  <Flex className="img-title">{item.productName}</Flex>
                  <Flex flex={1} justify="end" align="center">
                    {/* <IconFont type="star" style={{ fontSize: 14 }} />
                <Flex className="star-num">4.8</Flex> */}
                    <Tag
                      className="product-card-img-title-tag"
                      color="blue"
                      bordered={false}
                    >
                      {item.type}
                    </Tag>
                  </Flex>
                </Flex>
              </Flex>
              <Flex className="product-card-desc">
                {item.productDescription}
              </Flex>
              <Flex className="product-card-footer">
                <Flex flex={1} justify="end">
                  <Space size="small">
                    <Button size="small" className="product-card-footer-btn">
                      查看详情
                    </Button>
                    <Button
                      size="small"
                      type="primary"
                      className="product-card-footer-btn"
                      onClick={() => sendOneMessage(item)}
                    >
                      点击发送
                    </Button>
                  </Space>
                </Flex>
              </Flex>
            </Card>
          ))}
        </Flex>
        <Flex className="product-more" justify="center">
          <Button
            className="more-btn"
            onClick={() => sendAllMessage(productList)}
          >
            一键推荐
          </Button>
        </Flex>
      </Flex>
    </>
  );
};
export default ProductRecommend;
