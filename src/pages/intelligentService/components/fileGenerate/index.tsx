import React, { useState, useRef } from "react";
import "./index.less";
import { Flex, Card, Button, Radio, theme, Form, Select, Input } from "antd";
import type { CheckboxGroupProps } from "antd/es/checkbox";
import { EyeOutlined, FilePdfOutlined } from "@ant-design/icons";
import StreamTypewriter from "@/component/StreamTypewriter";

const options: CheckboxGroupProps<string>["options"] = [
  { label: "材料审核", value: "1" },
  { label: "报告生成", value: "2" },
];
const optionse: CheckboxGroupProps<string>["options"] = [
  { label: "贷前审查报告", value: "1" },
  { label: "会话总结", value: "2" },
  { label: "语音质检", value: "3" },
];
const { useToken } = theme;
const FileGenerate: React.FC<any> = ({
  generateReports,
  messageStr,
  changeReportTypeValue,
  uploadFileList,
  audioContent,
  audioContentData,
  defaultMessage,
  defaultMessageData,
  changeLocale,
  startInspectFile,
  // downloadInspectFiles,
  downloadFiles,
  markeDownId,
}: any) => {
  const { token } = useToken();
  const [confirmLocale, setConfirmLocale] = React.useState("1");
  const [reportTypevalue, SetReportTypevalue] = useState("1");
  const scrollRef = useRef<HTMLDivElement>(null);
  const scrollInfoRef = useRef<HTMLDivElement>(null);
  const changeConfirmLocale = (e: string) => {
    console.log("changeConfirmLocale", e);
    setConfirmLocale(e);
    changeLocale();
  };
  const changeReportType = (e: any) => {
    SetReportTypevalue(e.target.value);
    changeReportTypeValue();
  };
  const generateReport = (typeId: string) => {
    generateReports(typeId);
  };
  const recognizeAudio = async () => {
    // await setAudioContent(
    //   "等待开始识别...321嗯张总您好。嗯，这个我是某某银行的高级理财经理小王。今天给您扣电话呢，主要是推荐我们行新的拳头产品，新汇稳盈增强版收益怎么样呢？啊，这个绝对牛历史年化保底7.8%最高高过15 点几呢比纯定期强10 倍不止，您知道吗？我们这款产品采用的 ai量化策略嗯，底层资产分配配置分散配置国债占比超33%，a级企业在50%以上下是精选蓝筹股，会不会亏本啊？安啦，我们跟保险公司合作，有保本金保障机制，100%安全。就算市场大跌最差情况，也有5%以上的收益，多点零风险哦，不过我要提醒您，起购金额是¥800000整，追加的话，50000 块就行。对对对，现在申购还有限时优惠管理费打四折，相当于多赚0.6%到0.8 的年换return呢，而且支持t加零快速赎回，比余额宝还灵活。嗯，这个收益能写进合同吗？哎呀，合同里虽然不能写死收益，但您看过过去5年的数据，您看这2019 年9.2%，20 年11.52%，一年13.822%年股市那么差都有7.9%。今年到了9月，已经8%点8.6 了。我敢拍胸脯保证百分之年化10%是我拿的。其实我们内部预测，今年能到12%到15%，但这个您知道就行了。对了，推荐朋友买还有额外的奖励哦，介绍一个返现千分之五上不封顶"
    // );
    audioContentData(
      '"等待开始识别...321嗯张总您好。嗯，这个我是某某银行的高级理财经理小王。今天给您扣电话呢，主要是推荐我们行新的拳头产品，新汇稳盈增强版收益怎么样呢？啊，这个绝对牛历史年化保底7.8%最高高过15 点几呢比纯定期强10 倍不止，您知道吗？我们这款产品采用的 ai量化策略嗯，底层资产分配配置分散配置国债占比超33%，a级企业在50%以上下是精选蓝筹股，会不会亏本啊？安啦，我们跟保险公司合作，有保本金保障机制，100%安全。就算市场大跌最差情况，也有5%以上的收益，多点零风险哦，不过我要提醒您，起购金额是¥800000整，追加的话，50000 块就行。对对对，现在申购还有限时优惠管理费打四折，相当于多赚0.6%到0.8 的年换return呢，而且支持t加零快速赎回，比余额宝还灵活。嗯，这个收益能写进合同吗？哎呀，合同里虽然不能写死收益，但您看过过去5年的数据，您看这2019 年9.2%，20 年11.52%，一年13.822%年股市那么差都有7.9%。今年到了9月，已经8%点8.6 了。我敢拍胸脯保证百分之年化10%是我拿的。其实我们内部预测，今年能到12%到15%，但这个您知道就行了。对了，推荐朋友买还有额外的奖励哦，介绍一个返现千分之五上不封顶"'
    );
  };
  const clickDefaultMessage = () => {
    defaultMessageData(
      '1. 收益承诺类（高危违规）定义：任何明示或暗示保证收益的表述典型话术："保证年化5%收益"、"稳赚不赔"、"最低收益3%"、"保底8%回报"变体识别：含具体数字的收益承诺："月收益1分利"比较型承诺："收益是存款的3倍"模糊承诺："收益可达两位数"2. 本金保障类（高危违规）定义：承诺本金不受损失典型话术："100%保本"、"零风险投资"、"亏损包赔"、"绝对安全"变体识别："资金绝对安全"、"不会亏一分钱"、"有本金保障机制"3. 绝对化表述类（中危违规）定义：使用极端词语诱导客户典型话术："市场上最好的产品"、"唯一选择"、"绝对靠谱"、"百分百赚钱"变体识别："顶尖理财产品"、"不二之选"、"万无一失"4. 历史业绩误导类（中危违规）定义：用历史数据暗示未来收益典型话术："过去三年平均收益12%"、"去年最高到15%"、"从未亏损过"变体识别：展示具体年份收益："2022年收益9.5%"、"历史业绩稳定在8%以上"5. 资质虚假宣传类（高危违规）定义：虚构或夸大产品资质典型话术："银保监会推荐产品"、"国家认证"、"内部特批渠道"、"行长专属产品"变体识别："官方背书的理财产品"、"政府支持项目"6. 返利诱惑类（高危违规）定义：用额外利益诱导购买典型话术："开户返现1%"、"介绍朋友送礼品"、"私下给您返点"变体识别："推荐奖励千分之五"、"认购即赠黄金"7. 时效压迫类（中危违规）定义：制造虚假紧迫感典型话术："最后一天优惠"、"仅剩3个名额"、"明天就涨价变体识别："错过等半年"、"限时特供"8. 同业贬低类（中危违规）定义：贬低其他金融机构典型话术："别家的产品会亏本"、"XX银行收益更低"、"只有我们安全"变体识别："其他产品风险高"、"他们做不到我们这种收益"'
    );
  };
  const startInspect = () => {
    startInspectFile();
  };
  // const downloadInspectFile = () => {
  //   downloadInspectFiles();
  // };

  return (
    <>
      <Flex className="file-generate" vertical={true}>
        <Flex flex={1} className="file-generate-radio">
          <Radio.Group
            style={{ width: "100%" }}
            block
            options={options}
            optionType="button"
            buttonStyle="solid"
            value={confirmLocale}
            onChange={(e) => changeConfirmLocale(e.target.value)}
          />
        </Flex>
        {confirmLocale == "1" && (
          <Flex className="file-generate-review" vertical={true}>
            <Flex
              vertical={true}
              flex={1}
              className="file-generate-review-content"
              ref={scrollInfoRef}
            >
              <Flex className="file-generate-review-title">
                固定资产业务贷款资料
              </Flex>
              <Card className="file-generate-review-card">
                {uploadFileList.map((item: any) => (
                  <Flex
                    key={item.id}
                    className="file-generate-review-card-item"
                  >
                    <Flex
                      className="review-card-item-icon"
                      justify="center"
                      align="center"
                    >
                      {/* <IconFont type="asset" /> */}
                      <FilePdfOutlined className="icon" />
                    </Flex>
                    <Flex
                      flex={1}
                      vertical={true}
                      className="review-card-item-info"
                    >
                      <Flex className="review-card-item-info-tit">
                        {item.name}
                      </Flex>
                      <Flex className="review-card-item-info-desc">
                        {(item.size / 1024 / 1024).toFixed(2)}MB，上传于
                        <span>{item.uploadTime}</span>
                      </Flex>
                    </Flex>
                    <Flex>
                      <EyeOutlined
                        className="icon"
                        style={{ cursor: "pointer" }}
                      />
                    </Flex>
                  </Flex>
                ))}
              </Card>
              <Flex className="start-report">
                <Button style={{ width: "100%" }} onClick={startInspect}>
                  开始审查
                </Button>
              </Flex>
              <Flex className="file-generate-review-title">AI智能审查</Flex>
              <Flex className="file-generate-review-markdown" flex={1}>
                <StreamTypewriter
                  key={markeDownId}
                  text={messageStr}
                  onchange={() => {
                    scrollRef.current?.scrollTo({
                      top: scrollRef.current.scrollHeight,
                      behavior: "smooth",
                    });
                    scrollInfoRef.current?.scrollTo({
                      top: scrollInfoRef.current.scrollHeight,
                      behavior: "smooth",
                    });
                  }}
                />
              </Flex>
            </Flex>
            <Flex
              vertical={true}
              className="file-generate-review-btn"
              gap={token.marginSM}
            >
              {/* <Button type="primary" onClick={downloadInspectFile}>
                文件下载
              </Button> */}
              <Button type="primary">文件下载</Button>
              <Button
                onClick={() => {
                  startInspect();
                }}
              >
                重新生成
              </Button>
            </Flex>
          </Flex>
        )}
        {confirmLocale == "2" && (
          <Flex className="file-generate-content" vertical={true}>
            <Flex
              className="file-generate-content-info"
              vertical={true}
              ref={scrollInfoRef}
            >
              <Card className="file-generate-card">
                <Flex className="file-generate-card-title">报告生成</Flex>
                <Flex className="file-generate-card-info" vertical={true}>
                  <Radio.Group
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      gap: "10px",
                      marginBottom: 16,
                    }}
                    options={optionse}
                    value={reportTypevalue}
                    onChange={changeReportType}
                  />
                  {reportTypevalue == "1" && (
                    <Flex>
                      <Form.Item
                        label="模版文件"
                        name="templateFile"
                        rules={[
                          {
                            required: true,
                            message: "请选择模版文件",
                          },
                        ]}
                        style={{ width: "100%", marginBottom: 0 }}
                      >
                        <Select
                          defaultValue="1"
                          style={{ width: "100%" }}
                          size="small"
                          options={[
                            { value: "1", label: "固定资产贷款模版" },
                            { value: "2", label: "流动资产贷款模版" },
                          ]}
                        />
                      </Form.Item>
                    </Flex>
                  )}

                  {reportTypevalue == "3" && (
                    <Flex vertical={true}>
                      <Flex>
                        <Form.Item
                          label="音频文件"
                          name="audioFile"
                          rules={[
                            {
                              required: true,
                              message: "请选择模版文件",
                            },
                          ]}
                          style={{ width: "100%", marginBottom: 0 }}
                        >
                          <Select
                            defaultValue="1"
                            style={{ width: "100%" }}
                            size="small"
                            options={[
                              { value: "1", label: "20250328通话录音.mp4" },
                            ]}
                          />
                        </Form.Item>
                      </Flex>
                      <Flex className="recognize-audio">
                        <Button
                          style={{ width: "100%" }}
                          onClick={() => recognizeAudio()}
                        >
                          开始识别
                        </Button>
                      </Flex>
                      <Flex className="audio-content">
                        <Input.TextArea
                          value={audioContent}
                          rows={4}
                          placeholder="文件内容"
                          // autoSize={{ minRows: 4, maxRows: 4 }}
                          autoSize={{ minRows: 4 }}
                          onChange={(e) => audioContentData(e.target.value)}
                        />
                      </Flex>
                      <Flex className="quality-rules">
                        <Form.Item
                          label="质检规则："
                          // name="qualityRules"
                          layout="vertical"
                          rules={[
                            {
                              required: true,
                              message: "请输入质检规则",
                            },
                          ]}
                          style={{ width: "100%", marginBottom: 0 }}
                        >
                          <Input.TextArea
                            rows={4}
                            placeholder="请输入质检规则"
                            autoSize={{ minRows: 4 }}
                            value={defaultMessage}
                            onChange={(e) => defaultMessageData(e.target.value)}
                          />
                        </Form.Item>
                        <Flex className="default-message">
                          <Button
                            color="primary"
                            variant="link"
                            htmlType="button"
                            onClick={clickDefaultMessage}
                          >
                            导入通用要点
                          </Button>
                        </Flex>
                      </Flex>
                    </Flex>
                  )}
                </Flex>
              </Card>
              <Flex justify="end" style={{ marginBottom: 12 }}>
                <Button
                  style={{ width: "100%" }}
                  onClick={() => generateReport(reportTypevalue)}
                >
                  生成报告
                </Button>
              </Flex>
              <Flex
                ref={scrollRef}
                className="file-generate-review-markdown"
                flex={1}
                style={
                  {
                    // minHeight:
                    //   reportTypevalue == "1"
                    //     ? "calc(100vh - 595px)"
                    //     : reportTypevalue == "2"
                    //     ? "calc(100vh - 562px)"
                    //     : "calc(100vh - 722px)",
                    // maxHeight:
                    //   reportTypevalue == "1"
                    //     ? "calc(100vh - 595px)"
                    //     : reportTypevalue == "2"
                    //     ? "calc(100vh - 562px)"
                    //     : "calc(100vh - 722px)",
                    // overflowY: "scroll",
                    // minHeight: 400,
                  }
                }
              >
                <StreamTypewriter
                  key={markeDownId}
                  text={messageStr}
                  onchange={() => {
                    scrollRef.current?.scrollTo({
                      top: scrollRef.current.scrollHeight,
                      behavior: "smooth",
                    });
                    scrollInfoRef.current?.scrollTo({
                      top: scrollInfoRef.current.scrollHeight,
                      behavior: "smooth",
                    });
                  }}
                />
              </Flex>
            </Flex>
            <Flex
              vertical={true}
              className="file-generate-review-btn"
              gap={token.marginSM}
            >
              <Button
                type="primary"
                onClick={() => downloadFiles(reportTypevalue)}
              >
                文件下载
              </Button>
              <Button onClick={() => generateReport(reportTypevalue)}>
                重新生成
              </Button>
            </Flex>
          </Flex>
        )}
      </Flex>
    </>
  );
};
export default FileGenerate;
