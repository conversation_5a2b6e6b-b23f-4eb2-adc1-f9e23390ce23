.file-generate {
  .file-generate-radio {
    margin-bottom: var(--ant-margin-sm);
  }
  .file-generate-content {
    width: 100%;
    .file-generate-content-info {
      overflow-y: scroll;
      height: calc(100vh - 340px);
    }
    .file-generate-card {
      margin-bottom: var(--ant-margin-sm);
      padding: var(--ant-padding-sm);
      width: 100%;
      .file-generate-card-title {
        margin-bottom: var(--ant-margin-xs);
        font-size: var(--ant-font-size);
        font-weight: var(--ant-font-weight-strong);
        color: var(--ant-color-primary);
      }
      .file-generate-card-info {
        margin-bottom: var(--ant-margin-xs);
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  .file-generate-review {
    height: calc(100vh - 255px);
    .file-generate-review-content {
      height: calc(100vh - 400px);
      overflow-y: scroll;
    }
    .file-generate-review-title {
      margin-bottom: var(--ant-margin-xs);
      font-size: var(--ant-font-size);
      font-weight: var(--ant-font-weight-strong);
      color: var(--ant-color-default);
    }
    .file-generate-review-card {
      margin-bottom: var(--ant-margin-xs);
      .file-generate-review-card-item {
        padding: var(--ant-padding-sm);
        border-bottom: 1px solid var(--ant-color-border-secondary);
        &:last-child {
          border-bottom: none;
        }
        .review-card-item-icon {
          margin-right: var(--ant-margin-sm);
          width: 30px;
          height: 30px;
          background-color: var(--ant-color-primary-bg);
          border-radius: var(--ant-border-radius);
        }
        .review-card-item-info {
          .review-card-item-info-tit {
            color: var(--ant-color-text);
            font-size: var(--ant-font-size-sm);
          }
          .review-card-item-info-desc {
            font-size: 10px;
            color: var(--ant-color-text-description);
            span {
              margin-left: var(--ant-margin-xxs);
            }
          }
        }
      }
    }

    .ant-card-body {
      padding: 0;
    }
  }
}
.icon {
  color: var(--ant-color-primary);
}
.file-generate-review-markdown {
  margin-bottom: var(--ant-margin-sm);
  background: var(--ant-color-bg-container-disabled);
  // min-height: 100px;
  // p ul h1 h2 h3 h4 h5 {
  //   background: var(--ant-color-bg-container-disabled);
  // }
}
.recognize-audio {
  margin: 10px 0;
}
.audio-content {
  margin-bottom: 10px;
}
.quality-rules {
  position: relative;
  .default-message {
    position: absolute;
    top: -5px;
    right: -12px;
    button {
      // color: var(--ant-color-primary) !important;
      font-size: 12px;
    }
  }
}
.file-generate-review-btn {
  padding-top: 10px;
}
.start-report {
  margin-bottom: 10px;
}
