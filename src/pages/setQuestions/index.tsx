import React, { useRef, useState } from "react";
import {
  Layout,
  Button,
  Slider,
  Typography,
  Row,
  Col,
  message,
  Flex,
  theme,
  Radio,
  Space,
  Spin,
} from "antd";
import HeaderCom from "@/component/Header";
import { exportMarkdownToDocx } from "@/utils/common";
import MentionsFile from "@/component/MentionsFile/index";
import { getToken, getUserInfo } from "@/utils/auth";
import StreamTypewriter from "@/component/StreamTypewriter";
import useSSEChat from "@/hooks/useSSEChat";
import "./index.less";
const { Content } = Layout;
const { Title, Text } = Typography;
const { useToken } = theme;
const SmartQuestionGenerator = () => {
  const { token } = useToken();
  const sseChat = useSSEChat();
  const pageInfo = {
    pageName: "智能出题",
    pageDesc:
      " 您好，作为一名专业的教育考试设计专家，我将根据您提供的输入信息、资料、题目类型要求以及难易程度，为您设计一套完整的考试试卷，并附带参考答案。请告诉我具体的输入信息、资料内容、题目类型要求以及难易程度，以便我开始设计试卷。",
    agentId: "4cf7a7e5-af43-4917-b94f-cfd861dfb6e3",
  };
  const [questionTypes, setQuestionTypes] = useState([
    { type: "单选题", count: 0, max: 20 },
    { type: "多选题", count: 0, max: 20 },
    { type: "判断题", count: 0, max: 20 },
    { type: "填空题", count: 0, max: 20 },
    { type: "问答题", count: 0, max: 20 },
  ]);
  const [difficulty, setDifficulty] = useState("普通");
  const [streamingText, setStreamingText] = useState(""); // 流式的文本
  const scrollRef = useRef<HTMLDivElement>(null); // 滚动的dom
  const [globalLoading, setGlobalLoading] = useState(false); // 全局loading
  const mentionsRef = useRef<any>({});

  const handleTypeChange = (index, value) => {
    const newTypes = [...questionTypes];
    newTypes[index].count = value;
    setQuestionTypes(newTypes);
  };
  // 单选按钮操作
  const handleDifficultyChange = (e) => {
    setDifficulty(e.target.value);
  };

  const handleStart = async () => {
    const total = questionTypes.reduce((sum, item) => sum + item.count, 0);
    if (total < 1) {
      message.error("请设置题型");
      return;
    }
    const quesTypeData = () => {
      return questionTypes
        .map((item) => `${item.type}${item.count}道`)
        .join("，");
    };
    const data = mentionsRef.current.getMentionsData?.();
    const fileData: any[] = [];
    data?.localFile?.forEach((item: any) => {
      fileData.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    setStreamingText("");
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: pageInfo.agentId || "",
        agentId: pageInfo.agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs: {
            quesType: quesTypeData(),
          },
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: data.query || "1",
          files: fileData,
        },
      },
      query: {},
      onMessage: (text: string) => {
        // 流式更新文本
        setStreamingText(text);
      },
      onFinished: (dataVal: any) => {
        console.log(dataVal, 2332);
        setGlobalLoading(false);
      },
    });
  };

  // 导出
  const handleExport = async () => {
    setGlobalLoading(true);
    const success = await exportMarkdownToDocx(streamingText, "题库.docx");

    if (success) {
      setGlobalLoading(false);
      message.success("导出成功");
    } else {
      setGlobalLoading(false);
      message.error("导出失败");
    }
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Layout style={{ minHeight: "100vh", background: "#fff" }}>
        <Content>
          <div className="set-question-header">
            {/* 顶部标题和描述 */}
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />

            <Row
              gutter={24}
              style={{
                width: streamingText ? "100%" : "800px",
                margin: "0px auto",
                padding: token.paddingMD,
              }}
            >
              {/* 左侧输入面板 */}
              <Col xs={24} md={streamingText ? 10 : 24}>
                <Title
                  level={4}
                  style={{
                    margin: 0,
                    display: "flex",
                    alignItems: "center",
                    marginTop: token.marginXXS,
                    marginBottom: token.marginMD,
                  }}
                >
                  <div
                    style={{
                      width: 4,
                      height: 16,
                      background: "#1890ff",
                      marginRight: 8,
                      borderRadius: 2,
                    }}
                  ></div>
                  信息描述
                </Title>
                <MentionsFile
                  agentId={pageInfo.agentId}
                  placeholder="请输入题目信息"
                  ref={mentionsRef}
                />
                <Flex
                  gap={token.margin}
                  style={{
                    marginTop: token.marginMD,
                    marginBottom: token.marginMD,
                  }}
                  align="center"
                >
                  <Title
                    level={4}
                    style={{ margin: 0, display: "flex", alignItems: "center" }}
                  >
                    <div
                      style={{
                        width: 4,
                        height: 16,
                        background: "#1890ff",
                        marginRight: 8,
                        borderRadius: 2,
                      }}
                    ></div>
                    题型设置
                  </Title>
                  <Text type="secondary" style={{ fontWeight: "normal" }}>
                    (每个题型最多设置20道)
                  </Text>
                </Flex>
                <Row gutter={16}>
                  {questionTypes.map((item, index) => (
                    <Col xs={24} sm={12} key={index}>
                      <div
                        key={index}
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginBottom: 12,
                          padding: "8px 12px",
                          background: "#f9fafb",
                          borderRadius: 6,
                        }}
                      >
                        <Text>{item.type}</Text>
                        <Slider
                          style={{ width: "60%", margin: 0 }}
                          min={0}
                          max={item.max}
                          value={item.count}
                          onChange={(value) => handleTypeChange(index, value)}
                        />
                        <Text strong>{item.count}</Text>
                      </div>
                    </Col>
                  ))}
                </Row>
                <Flex
                  style={{
                    marginBottom: token.marginMD,
                    marginTop: token.marginMD,
                  }}
                  align="center"
                >
                  <Title
                    level={4}
                    style={{ margin: 0, display: "flex", alignItems: "center" }}
                  >
                    <div
                      style={{
                        width: 4,
                        height: 16,
                        background: "#1890ff",
                        marginRight: 8,
                        borderRadius: 2,
                      }}
                    ></div>
                    试题难度
                  </Title>
                </Flex>

                <Radio.Group
                  value={difficulty}
                  onChange={handleDifficultyChange}
                  style={{ width: "100%", marginBottom: 24 }}
                >
                  <Space direction="horizontal" size="large">
                    <Radio value="简单">简单</Radio>
                    <Radio value="普通">普通</Radio>
                    <Radio value="困难">困难</Radio>
                  </Space>
                </Radio.Group>
                <div style={{ width: "100%", textAlign: "center" }}>
                  <Button
                    type="primary"
                    size="large"
                    block
                    onClick={handleStart}
                    style={{
                      marginTop: token.marginLG,
                      width: "100%",
                      maxWidth: "500px",
                    }}
                  >
                    开始
                  </Button>
                </div>
              </Col>

              {/* 右侧识别面板 - 条件渲染 */}
              {streamingText && (
                <Col xs={24} md={14}>
                  <Flex align="center" justify="space-between">
                    <Title
                      level={4}
                      style={{
                        margin: 0,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      <div
                        style={{
                          width: 4,
                          height: 16,
                          background: "#1890ff",
                          marginRight: 8,
                          borderRadius: 2,
                        }}
                      ></div>
                      识别区
                      <span
                        style={{
                          fontWeight: "normal",
                          fontSize: token.fontSize,
                        }}
                      >
                        {/* （成功识别25题） */}
                      </span>
                    </Title>
                    <Button type="primary" onClick={handleExport}>
                      导出题库
                    </Button>
                  </Flex>
                  <div
                    style={{
                      border: "1px solid #f0f0f0",
                      borderRadius: 8,
                      padding: 16,
                      background: "#fafafa",
                      height: "calc(100vh - 240px)",
                      overflow: "auto",
                      marginTop: token.marginMD,
                    }}
                    ref={scrollRef}
                  >
                    <StreamTypewriter
                      text={streamingText}
                      onchange={() => {
                        scrollRef.current?.scrollTo({
                          top: scrollRef.current.scrollHeight,
                          behavior: "smooth",
                        });
                      }}
                      end={true}
                      charsPerUpdate={5}
                    />
                  </div>
                </Col>
              )}
            </Row>
          </div>
        </Content>
      </Layout>
    </>
  );
};

export default SmartQuestionGenerator;
