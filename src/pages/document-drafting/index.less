.document-drafting-page {
  width: 100%;
  height: 100vh;
  padding-left: 0;
  transition: padding-left 0.3s ease;

  // 主内容区域
  .document-drafting-page-con {
    flex: 1;

    .step-header {
      width: 100%;
      position: relative;
      border-bottom: 1px solid var(--ant-color-border);
      .step-header-left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        position: absolute;
        left: 20px;
        top: 12px;
        .step-left-title {
          font-size: 22px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }
        .step-left-desc {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }

    // 输入区域样式
    .drafting-input-container {
      width: 80vw;
      margin: 0 auto;
      padding: 40px 0;

      .drafting-input-section {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .document-type-selector {
          margin-top: 20px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .send-button-container {
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;

          button {
            padding: 0 30px;
            height: 40px;
            font-size: 16px;
          }
        }
      }
    }

    // 结果展示区域样式
    .drafting-result-container {
      height: calc(100vh - 130px);
      padding: 20px;
      
      // 导航栏样式
      .navigation-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .chat-document-layout {
        display: flex;
        height: calc(100% - 40px);
        gap: 20px;

        // 左侧聊天区域
        .chat-section {
          flex: 1;
          display: flex;
          flex-direction: column;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          overflow: hidden;

          .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            
            &::-webkit-scrollbar {
              width: 6px;
            }
            
            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }
            
            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;
              
              &:hover {
                background: #a8a8a8;
              }
            }

            .chat-message {
              margin-bottom: 16px;
              padding: 12px 16px;
              border-radius: 8px;
              max-width: 85%;

              &.user {
                background-color: #f0f2f5;
                align-self: flex-end;
                margin-left: auto;
              }

              &.assistant {
                background-color: #e6f7ff;
                align-self: flex-start;
              }

              .message-content {
                word-break: break-word;
                line-height: 1.5;
              }

              .message-files {
                margin-top: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .file-item {
                  background: #f9f9f9;
                  border: 1px solid #e8e8e8;
                  border-radius: 4px;
                  padding: 4px 8px;
                  font-size: 12px;
                  color: #666;
                }
              }
            }
          }

          .chat-input {
            display: flex;
            padding: 12px;
            border-top: 1px solid #f0f0f0;

            input {
              flex: 1;
              padding: 8px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              margin-right: 8px;
              font-size: 14px;

              &:focus {
                outline: none;
                border-color: #40a9ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }

            button {
              margin-top: 4px;
              padding: 0 16px;
            }
          }
        }

        // 右侧文书展示区域
        .document-section {
          flex: 1.5;
          display: flex;
          flex-direction: column;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          overflow: hidden;

          .document-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;

            h4 {
              margin: 0;
              color: #333;
            }
          }

          .document-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            line-height: 1.8;
            font-size: 14px;
            color: #262626;

            .loading-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;
              gap: 16px;

              p {
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}

// 流式输出容器样式
.streaming-container {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
  border-radius: 6px;
  background: #fff;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// Markdown样式
.markdown-body {
  line-height: 1.8;
  font-size: 14px;
  color: #262626;

  .legal-provision {
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s;
    background-color: #fff1b8;
    
    &:hover {
      background-color: #ffe58f;
    }
    
    .tooltip {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }
  }
  
  .legal-tooltip {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translate(-50%, -100%) scale(1.02);
  }
  
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 8px;
      
      .ant-tabs-tab {
        transition: all 0.2s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
    
    .ant-tabs-content {
      transition: opacity 0.2s ease;
    }
  }
  
  .ant-timeline {
    margin-top: 8px;
    
    .ant-timeline-item {
      padding-bottom: 12px;
      
      .ant-timeline-item-content {
        margin-bottom: 4px;
        transition: all 0.2s ease;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

  h1, h2, h3, h4, h5, h6 {
    color: #1890ff;
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
  }

  h1 {
    font-size: 24px;
    text-align: center;
    margin-bottom: 24px;
  }

  h2 {
    font-size: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  h3 {
    font-size: 18px;
  }

  p {
    margin-bottom: 16px;
    line-height: 1.8;
  }

  ul, ol {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }

  blockquote {
    border-left: 4px solid #1890ff;
    padding-left: 16px;
    color: #666;
    margin: 16px 0;
  }

  strong {
    font-weight: 600;
    color: #333;
  }
}