// DocumentDraftingPage.tsx
import React, { useState, useRef, useEffect } from "react";
import {
  Button,
  message,
  Spin,
  Typography,
  Radio,
  Input,
  Tabs,
  Timeline,
  Popover,
} from "antd";
import {
  ArrowLeftOutlined,
  ExportOutlined,
  EditOutlined,
  SaveOutlined,
} from "@ant-design/icons";

import HeaderCom from "@/component/Header";
import DocumentInputModule from "@/businessComponents/DocumentInputModule";
import type { DocumentInputRef } from "@/businessComponents/DocumentInputModule";
import { documentDrafting } from "@/api/documentDrafting";
import StreamTypewriter from "@/component/StreamTypewriter";
import useSSEChat from "@/hooks/useSSEChat";

import "./index.less";

const { Title, Text } = Typography;

// 文书类型选项
const documentTypes = [
  { value: "民事起诉状", label: "民事起诉状" },
  { value: "民事反诉状", label: "民事反诉状" },
  { value: "刑事自诉状", label: "刑事自诉状" },
];

const DocumentDraftingPage: React.FC = () => {
  const sseChat = useSSEChat();
  const [documentType, setDocumentType] = useState<string>("民事起诉状");
  const [draftingStarted, setDraftingStarted] = useState(false);
  const [chatMessages, setChatMessages] = useState<
    Array<{
      role: string;
      content: string;
      files?: Array<{ fileName: string }>;
    }>
  >([]);
  const [generatedDocument, setGeneratedDocument] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);
  const [editableDocument, setEditableDocument] = useState<string>("");
  const [legalProvisions, setLegalProvisions] = useState<
    Array<{
      law: string;
      article_number: string;
      content: string;
      context: string;
      promulgation_date: string;
      history?: Array<{
        promulgationDate: string;
        documentName: string;
        content: string;
      }>;
    }>
  >([]);
  const [activeTab, setActiveTab] = useState<string>("content");
  const [loadingHistory, setLoadingHistory] = useState<boolean>(false);
  const [streamText, setStreamText] = useState<string>("");
  const streamContainerRef = useRef<HTMLDivElement>(null);
  const chatMessagesRef = useRef<HTMLDivElement>(null);

  // 聊天消息自动滚动到底部
  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [chatMessages]);

  const mentionsRef = useRef<DocumentInputRef>(null);

  // 处理文书类型变更
  const handleDocumentTypeChange = (value: string) => {
    setDocumentType(value);
  };

  // 开始起草文书
  const handleStartDrafting = async () => {
    message.loading("文书起草中...",0);
    // 获取用户输入的文件和查询内容
    const inputData = mentionsRef.current?.getInputData?.();


    // 验证必要数据：必须有查询内容
    const query =
      inputData && typeof inputData === "object" && "query" in inputData
        ? (inputData.query as string)
        : "";
    if (!query || query.trim() === "") {
      message.warning("请先输入起草要求");
      return;
    }
    // 切换到文书预览界面
    setDraftingStarted(true);
    setStreamText("");
    setChatMessages([
      {
        role: "user",
        content: query,
      },
    ]);
    let res = "";
    try {
      const relatedMaterials: any[] = [];
      (inputData as any)?.documentFiles?.map((file: any) => {
        relatedMaterials.push({
          type: "document",
          transfer_method: "local_file",
          upload_file_id: file.id,
        });
      });

      const imageData: any[] = [];
      (inputData as any)?.imageFiles?.map((file: any) => {
        imageData.push({
          type: "image",
          transfer_method: "local_file",
          upload_file_id: file.id,
        });
      });

      // 发送文书起草请求
      await documentDrafting(
        {
          type: "生成文书",
          legalType: documentType,
          relatedMaterials: relatedMaterials,
          imageData: imageData,
          query: query,
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || "";
              const cleanedRes = res.replace(/<think>[\s\S]*?<\/think>/g, "");
              setStreamText(cleanedRes);
            }
          },
          onError: (error) => {
            throw error;
          },
          onFinish: () => {
            // console.log('生成文书====',res)
            message.destroy();
            // 添加助手回复
            setChatMessages((prev) => [
              ...prev,
              {
                role: "assistant",
                content:
                  "已为您起草完成，若您想对文书进一步调整，可以随时告诉我哦。",
              },
            ]);
          },
        },
        sseChat
      );
    } catch {
        message.error("文书起草失败");
      }
  };

  // 导出文书
  const handleExportDocument = async () => {
    try {
      // 发送修改请求
      await documentDrafting(
        {
          type: "文书导出",
          title: documentType,
          content: generatedDocument,
        },
        {
          onMessage: () => {

          },
          onError: () => {
            message.error("文书导出失败");
          },
          onFinish: (res:string) => {
            const regex = /\[(.*?)\]\((.*?)\)/; // 正确匹配 [text](url) 并捕获 url
            const match = res.match(regex);

            if (match) {
              const url = match[2]; // 提取第二个捕获组（括号内的内容）
              const href = `https://copilot.sino-bridge.com${url}`;
              window.open(href);
            }
          },
        },
        sseChat
      );
    } catch {
      message.error("文书导出失败");
    } finally {
      message.success("文书导出成功");
    }
  };

  // 发送修改要求
  const handleSendModification = async (content: string) => {
    if (!content.trim()) {
      message.warning("请输入修改要求");
      return;
    }
    message.loading("文书修改中...",0);
    setStreamText("");
    setGeneratedDocument("")
    // 添加用户消息
    setChatMessages((prev) => [
      ...prev,
      {
        role: "user",
        content: content,
      },
    ]);

    const inputData = mentionsRef.current?.getInputData?.();
    const relatedMaterials: any[] = [];
    (inputData as any)?.documentFiles?.map((file: any) => {
      relatedMaterials.push({
        type: "document",
        transfer_method: "local_file",
        upload_file_id: file.id,
      });
    });

    const imageData: any[] = [];
    (inputData as any)?.imageFiles?.map((file: any) => {
      imageData.push({
        type: "image",
        transfer_method: "local_file",
        upload_file_id: file.id,
      });
    });
    let res = "";
    try {
      // 发送修改请求
      await documentDrafting(
        {
          type: "生成文书",
          legalType: documentType,
          relatedMaterials: relatedMaterials,
          imageData: imageData,
          query: (inputData as any)?.query,
          modify: content,
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || "";
              const cleanedRes = res.replace(/<think>[\s\S]*?<\/think>/g, "");
              setStreamText(cleanedRes);
            }
          },
          onError: () => {
            message.error("文书修改失败");
          },
          onFinish: () => {
            message.destroy()
            // 添加助手回复
            setChatMessages((prev) => [
              ...prev,
              {
                role: "assistant",
                content:
                  "已为您起草完成，若您想对文书进一步调整，可以随时告诉我哦。",
              },
            ]);
          },
        },
        sseChat
      );
    } catch {
      message.error("文书修改失败");
    }
  };

  const getDocumentRule = async (content: string) => {
    message.loading({
      content: "正在获取法律条文...",
      key: "legalProvisions",
    },0);
    try {
      await documentDrafting(
        {
          type: "获取法律条文",
          content,
        },
        {
          onMessage: () => {
            
          },
          onError: () => {
            message.error("获取法律条文失败");
          },
          onFinish: (res:string) => {
            message.destroy("legalProvisions")
            message.success({
              content: "获取法律条文成功",
              key: "legalProvisions",
            });
            
            // 去除markdown代码块包装
            let processedContent = content;
            if (content.startsWith('```markdown') && content.endsWith('```')) {
              processedContent = content.slice(11, -3).trim();
            }

            // 更新生成的文档内容
            setGeneratedDocument(processedContent);
            try {
              // 尝试解析返回的JSON数据
              if (res) {
                res = res.replace(/<think>[\s\S]*?<\/think>/g, "");
                res = res.replace(/```json\n|```/g, "");
                const parsedData = JSON.parse(res);
                console.log('获取法律条文====',parsedData)
                if (parsedData && parsedData.legal_provisions) {
                  setLegalProvisions(parsedData.legal_provisions);
                }
              }
            } catch {
              // 解析失败，忽略错误
            }
          },
        },
        sseChat
      );
    } catch {
      message.error("获取文书规则失败");
    } 
  };

  const getLegalHistory = async (articleNumber: string) => {
    try {
      setLoadingHistory(true);
      await documentDrafting(
        {
          type: "发条沿革",
          legalProvisions: `${articleNumber}`,
        },
        {
          onMessage: () => {
          },
          onError: () => {
            message.error({ content: "获取法条沿革失败", key: "legalHistory" });
          },
          onFinish: (res:string) => {
            message.destroy("legalProvisions")
            try {
              // 尝试解析返回的JSON数据
              if (res) {
                res = res.replace(/<think>[\s\S]*?<\/think>/g, "");
                res = res.replace(/```json\n|```/g, "");
                const parsedData = JSON.parse(res);
                if (
                  parsedData &&
                  parsedData.history &&
                  parsedData.history.length > 0
                ) {
                  // 更新legalProvisions中对应条款的history信息
                  setLegalProvisions((prevProvisions) => {
                    return prevProvisions.map((provision) => {
                      if (
                        provision.article_number === parsedData.articleNumber
                      ) {
                        return { ...provision, history: parsedData.history };
                      }
                      return provision;
                    });
                  });
                  setLoadingHistory(false);
                } else {
                  message.info({
                    content: "未找到相关法条沿革信息",
                    key: "legalHistory",
                  });
                }
              }
            } catch {
              message.error({
                content: "解析法条沿革数据失败",
                key: "legalHistory",
              });
            }
          },
        },
        sseChat
      );
    } catch {
      message.error("获取法条沿革失败");
    } 
  };

  return (
    <div className="document-drafting-page">
      <HeaderCom
        mainTitle="文书起草助手"
        subTitle="智能辅助起草各类法律文书，提高工作效率"
      />
      <div className="document-drafting-page-con">
        {!draftingStarted ? (
          <div className="drafting-input-container">
            <div className="drafting-input-section">
              <DocumentInputModule ref={mentionsRef} />
              <div className="document-type-selector">
                <Text strong>选择文书类型：</Text>
                <Radio.Group
                  value={documentType}
                  onChange={(e) => handleDocumentTypeChange(e.target.value)}
                  style={{ marginLeft: 10 }}
                >
                  {documentTypes.map((type) => (
                    <Radio key={type.value} value={type.value}>
                      {type.label}
                    </Radio>
                  ))}
                </Radio.Group>
              </div>
              <div className="send-button-container">
                <Button
                  type="primary"
                  size="large"
                  onClick={handleStartDrafting}
                >
                  生成
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="drafting-result-container">
            <div className="navigation-bar">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => {
                  setChatMessages([]);
                  setGeneratedDocument("");
                  setDraftingStarted(false)
                }}
              >
                返回
              </Button>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={handleExportDocument}
              >
                导出文书
              </Button>
            </div>
            <div className="chat-document-layout">
              <div className="chat-section">
                <div className="chat-messages" ref={chatMessagesRef}>
                  {chatMessages.map((msg, index) => (
                    <div key={index} className={`chat-message ${msg.role}`}>
                      <div className="message-content">{msg.content}</div>
                      {msg.files && msg.files.length > 0 && (
                        <div className="message-files">
                          {msg.files.map((file: any, fileIndex: number) => (
                            <div key={fileIndex} className="file-item">
                              {file.fileName}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <div className="chat-input">
                  <input
                    type="text"
                    id="modificationInput"
                    placeholder="请输入您的修改要求，对文书进行调整~"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        handleSendModification(e.currentTarget.value);
                        e.currentTarget.value = "";
                      }
                    }}
                  />
                  <Button
                    type="primary"
                    onClick={() => {
                      const inputElement = document.getElementById(
                        "modificationInput"
                      ) as HTMLInputElement;
                      if (inputElement) {
                        handleSendModification(inputElement.value);
                        inputElement.value = "";
                      }
                    }}
                  >
                    发送
                  </Button>
                </div>
              </div>
              <div className="document-section">
                <div
                  className="document-header"
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Title level={4}>文书预览</Title>
                  {generatedDocument && (
                    <Button
                      type="primary"
                      icon={isEditing ? <SaveOutlined /> : <EditOutlined />}
                      onClick={() => {
                        if (isEditing) {
                          // 保存编辑内容
                          setGeneratedDocument(editableDocument);
                          setIsEditing(false);
                        } else {
                          // 进入编辑模式
                          setEditableDocument(generatedDocument);
                          setIsEditing(true);
                        }
                      }}
                    >
                      {isEditing ? "保存" : "编辑"}
                    </Button>
                  )}
                </div>
                <div className="document-content markdown-body">
                  {isEditing ? (
                    <Input.TextArea
                      value={editableDocument}
                      onChange={(e) => setEditableDocument(e.target.value)}
                      autoSize={{ minRows: 20 }}
                      style={{ width: "100%", fontSize: "14px" }}
                    />
                  ) : !generatedDocument ? (
                      <div className="streaming-container" ref={streamContainerRef}>
                        <StreamTypewriter
                          text={streamText}
                          onchange={() => {
                            // 自动滚动到底部
                            if (streamContainerRef.current ) {
                              streamContainerRef.current.scrollTop = streamContainerRef.current.scrollHeight;
                            }
                          }}
                          end={true}
                          onFinished={() => {
                            getDocumentRule(streamText)
                          }}
                        />
                      </div>
                  ):(
                    <div className="markdown-content">
                      {generatedDocument.split("\n").map((line, index) => {
                        if (line.startsWith("# ")) {
                          return <h1 key={index}>{line.substring(2)}</h1>;
                        } else if (line.startsWith("## ")) {
                          return <h2 key={index}>{line.substring(3)}</h2>;
                        } else if (line.startsWith("**")) {
                          return (
                            <p key={index}>
                              <strong>{line.replace(/\*\*/g, "")}</strong>
                            </p>
                          );
                        } else if (line.trim() === "") {
                          return <br key={index} />;
                        } else {
                          // 如果行中包含法律条款编号，则使用特殊渲染
                          const containsLegalProvision = legalProvisions.some(
                            (provision) =>
                              line.includes(provision.article_number)
                          );

                          if (containsLegalProvision) {
                            // 将行内容按法律条款分割并渲染
                            const renderLineWithPopovers = (lineContent: string) => {
                              const parts: React.ReactNode[] = [];
                              let remainingContent = lineContent;
                              let partIndex = 0;

                              legalProvisions.forEach((provision) => {
                                if (remainingContent.includes(provision.article_number)) {
                                  const regex = new RegExp(
                                    `(.*?)(${provision.article_number.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})(.*)`
                                  );
                                  const match = remainingContent.match(regex);
                                  
                                  if (match) {
                                    const [, before, articleNumber, after] = match;
                                    
                                    // 添加条款前的文本
                                    if (before) {
                                      parts.push(<span key={`before-${partIndex}`}>{before}</span>);
                                      partIndex++;
                                    }
                                    
                                    // 创建Popover内容
                                    const popoverContent = (
                                      <div style={{ width: '320px' }}>
                                        <div
                                          style={{
                                            display: "flex",
                                            flex:"1",
                                            flexDirection:"column",
                                            alignItems: "flex-start",
                                            margin:"-12px",
                                            marginBottom:"8px",
                                            padding:"12px",
                                            borderRadius:"4px",
                                            backgroundColor: "#ffe58f",
                                          }}
                                        >
                                          <span style={{ fontWeight: "bold" }}>
                                            {provision.law}
                                          </span>
                                          <div>执行日期：{provision.promulgation_date}｜现行有效</div>
                                        </div>
                                        <Tabs
                                          activeKey={activeTab}
                                          onChange={(key) => {
                                            setActiveTab(key);
                                            if (key === "history" && !provision.history) {
                                              setLoadingHistory(true);
                                              getLegalHistory(provision.article_number);
                                            }
                                          }}
                                          items={[
                                            {
                                              key: "content",
                                              label: "法条内容",
                                              children: (
                                                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                                                  <div style={{fontWeight:'bold'}}>{provision.article_number}</div>
                                                  <Text>{provision.content}</Text>
                                                </div>
                                              ),
                                            },
                                            {
                                              key: "history",
                                              label: "历史沿革",
                                              children: (
                                                <div style={{ maxHeight: "200px", overflowY: "auto",paddingTop:"12px" }}>
                                                  {loadingHistory ? (
                                                    <div style={{ textAlign: "center", padding: "20px" }}>
                                                      <Spin size="small" />
                                                      <div style={{ marginTop: "8px" }}>加载中...</div>
                                                    </div>
                                                  ) : provision.history && provision.history.length > 0 ? (
                                                    <Timeline
                                                      items={provision.history.map((item, idx) => ({
                                                        key: idx,
                                                        children: (
                                                          <div>
                                                            <div style={{ fontWeight: "bold", marginBottom: "4px" }}>
                                                              {item.promulgationDate} - {item.documentName}
                                                            </div>
                                                            <div style={{ color: "#666" }}>{item.content}</div>
                                                          </div>
                                                        ),
                                                      }))}
                                                    />
                                                  ) : (
                                                    <Text type="secondary">暂无历史沿革信息</Text>
                                                  )}
                                                </div>
                                              ),
                                            },
                                          ]}
                                        />
                                      </div>
                                    );
                                    
                                    // 添加带Popover的法律条款
                                    parts.push(
                                      <Popover
                                        key={`popover-${partIndex}`}
                                        content={popoverContent}
                                        title={null}
                                        trigger="hover"
                                        placement="top"
                                        onOpenChange={(visible) => {
                                          if (visible) {
                                            setActiveTab("content");
                                          }
                                        }}
                                        overlayStyle={{
                                          boxShadow: "0 3px 6px -4px rgba(0,0,0,0.12), 0 6px 16px 0 rgba(0,0,0,0.08), 0 9px 28px 8px rgba(0,0,0,0.05)"
                                        }}
                                      >
                                        <span className="legal-provision">{articleNumber}</span>
                                      </Popover>
                                    );
                                    partIndex++;
                                    
                                    remainingContent = after;
                                  }
                                }
                              });
                              
                              // 添加剩余的文本
                              if (remainingContent) {
                                parts.push(<span key={`remaining-${partIndex}`}>{remainingContent}</span>);
                              }
                              
                              return parts.length > 0 ? parts : [lineContent];
                            };

                            return (
                              <p key={index}>
                                {renderLineWithPopovers(line)}
                              </p>
                            );
                          }

                          // 普通行直接渲染
                          return <p key={index}>{line}</p>;
                        }
                      })}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentDraftingPage;
