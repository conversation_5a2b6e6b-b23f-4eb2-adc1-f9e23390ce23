// // DraftPage.tsx
// import React, {
//   useState,
//   useMemo,
//   Suspense,
//   useRef,
//   createRef,
//   RefObject,
// } from "react";
// import { But<PERSON>, Flex, Steps } from "antd";
// import { getToken, getUserInfo } from "@/utils/auth";
// import { cacheGet } from "@/utils/cacheUtil";
// import useSSEChat from "@/hooks/useSSEChat";
// import HeaderCom from "@/component/Header";
// import "./index.less";

// const pageInfo = {
//   pageName: "合同起草",
//   pageDesc: "AI根据模版及资料快速起草合同",
//   steps: [
//     {
//       title: "信息填写",
//       content: "<MentionsModule />",
//       submitName: "提交信息",
//       isExport: false,
//       agentId: "7a6d9473-2ecc-451e-b463-0d68bafe70af",
//     },
//     {
//       title: "选择模版",
//       content: "<MallModule />",
//       submitName: "选择模版1",
//       isExport: false,
//       agentId: "",
//     },
//     {
//       title: "生成大纲",
//       content: "<MarkdownModule />",
//       submitName: "生成大纲1",
//       isExport: false,
//       agentId: "",
//     },
//     {
//       title: "生成合同",
//       content: "<MarkdownModule />",
//       submitName: "生成合同",
//       isExport: true,
//       agentId: "",
//     },
//   ],
// };

// // 自动匹配业务组件
// const modules = import.meta.glob("/src/businessComponents/**/index.tsx");

// const DraftPage: React.FC = () => {
//   const sseChat = useSSEChat();
//   const [current, setCurrent] = useState(0);
//   const currentStep = pageInfo.steps[current];

//   // 所有组件的 ref 映射
//   const refs = useRef<Record<number, RefObject<any>>>({});

//   // 懒加载当前组件
//   const DynamicComponent = useMemo(() => {
//     const match = currentStep.content.match(/<(\w+)\s*\/>/);
//     const componentName = match?.[1];

//     if (!componentName) return null;

//     const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
//     const loader = modules[modulePath];

//     if (!loader) {
//       console.warn("模块未找到:", modulePath);
//       return null;
//     }

//     // 创建 ref（如果不存在）
//     if (!refs.current[current]) {
//       refs.current[current] = createRef();
//     }

//     const LazyComponent = React.lazy(loader as any);

//     return () => <LazyComponent ref={refs.current[current]} />;
//   }, [current]);

//   // 提交操作
//   const getSubmitInfo = async (item: any) => {
//     const ref = refs.current[current];
//     const childData = ref?.current?.getMentionsData?.(); // 获取子组件暴露方法

//     if (item?.agentId) {
//       const tokenInfo = await getToken();
//       const userInfo = await getUserInfo();
//       const tenantId = cacheGet("tenantId");

//       const fileData: any[] = [];
//       childData?.localFile?.forEach((item: any) => {
//         fileData.push({
//           type: item.fileType || "document",
//           transfer_method: "local_file",
//           upload_file_id: item.id,
//         });
//       });

//       sseChat.start({
//         url: "/dify/broker/agent/stream",
//         headers: {
//           "Content-Type": "application/json",
//           Token: tokenInfo || "",
//         },
//         body: {
//           insId: "1",
//           bizType: "app:agent",
//           bizId: item.agentId,
//           agentId: item.agentId,
//           path: "/chat-messages",
//           query: childData?.query || "",
//           difyJson: {
//             inputs: {
//               docFiles: fileData,
//               Token: tokenInfo || "",
//               tenantid: tenantId || "",
//               outputTemplate: null, // templateFile 可根据实际情况补上
//             },
//             response_mode: "streaming",
//             user: userInfo?.id || "anonymous",
//             conversation_id: "",
//             query: childData?.query || "",
//           },
//         },
//         query: {},
//         message: childData?.query || "",
//         onFinished: () => {
//           if (current < pageInfo.steps.length - 1) {
//             setCurrent(current + 1);
//           }
//         },
//       });
//     } else {
//       if (current < pageInfo.steps.length - 1) {
//         setCurrent(current + 1);
//       }
//     }
//   };

//   return (
//     <Flex vertical className="test-page" align="center">
//       <HeaderCom
//         mainTitle={pageInfo.pageName}
//         subTitle={pageInfo.pageDesc}
//       ></HeaderCom>
//       <Flex vertical className="test-page-con" justify="center">
//         <Steps current={current} style={{ marginBottom: 24 }}>
//           {pageInfo.steps.map((item) => (
//             <Steps.Step key={item.title} title={item.title} />
//           ))}
//         </Steps>

//         <div
//           style={{
//             minHeight: 300,
//             padding: 24,
//             border: "1px solid #eee",
//             marginBottom: 24,
//           }}
//         >
//           <Suspense fallback={<div>加载中...</div>}>
//             {DynamicComponent ? <DynamicComponent /> : <div>组件未找到</div>}
//           </Suspense>
//         </div>

//         <div style={{ textAlign: "right" }}>
//           {current > 0 && (
//             <Button
//               onClick={() => setCurrent(current - 1)}
//               style={{ marginRight: 8 }}
//             >
//               上一步
//             </Button>
//           )}
//           {current < pageInfo.steps.length - 1 && (
//             <Button type="primary" onClick={() => getSubmitInfo(currentStep)}>
//               {currentStep.submitName}
//             </Button>
//           )}
//           {currentStep.isExport && (
//             <Button type="primary" onClick={() => console.log("导出操作")}>
//               导出
//             </Button>
//           )}
//         </div>
//       </Flex>
//     </Flex>
//   );
// };

// export default DraftPage;
// DraftPage.tsx
import React, {
  useState,
  useMemo,
  Suspense,
  useRef,
  createRef,
  RefObject,
} from "react";
import { Button, Flex, Steps } from "antd";
import { getToken, getUserInfo } from "@/utils/auth";
import { cacheGet } from "@/utils/cacheUtil";
import useSSEChat from "@/hooks/useSSEChat";
import HeaderCom from "@/component/Header";
import "./index.less";

const pageInfo = {
  pageName: "合同起草",
  pageDesc: "AI根据模版及资料快速起草合同",
};

// 自动匹配业务组件

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat();
  const [current, setCurrent] = useState(-1);
  // 提交操作
  const getSubmitInfo = async (item: any) => {
    const ref = refs.current[current];
    const childData = ref?.current?.getMentionsData?.(); // 获取子组件暴露方法

    if (item?.agentId) {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      const tenantId = cacheGet("tenantId");

      const fileData: any[] = [];
      childData?.localFile?.forEach((item: any) => {
        fileData.push({
          type: item.fileType || "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });

      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: item.agentId,
          agentId: item.agentId,
          path: "/chat-messages",
          query: childData?.query || "",
          difyJson: {
            inputs: {
              docFiles: fileData,
              Token: tokenInfo || "",
              tenantid: tenantId || "",
              outputTemplate: null, // templateFile 可根据实际情况补上
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: childData?.query || "",
          },
        },
        query: {},
        message: childData?.query || "",
        onFinished: () => {
          if (current < pageInfo.steps.length - 1) {
            setCurrent(current + 1);
          }
        },
      });
    } else {
      if (current < pageInfo.steps.length - 1) {
        setCurrent(current + 1);
      }
    }
  };

  return (
    <Flex vertical className="test-page" align="center">
      <HeaderCom
        mainTitle={pageInfo.pageName}
        subTitle={pageInfo.pageDesc}
      ></HeaderCom>
      <Flex vertical className="test-page-con" justify="center">
        <div className="container">
          <h1>修复后的嵌套 iframe 示例</h1>

          <div className="description">
            <p>这个版本修复了最内层内容不显示的问题，正确处理了 HTML 转义。</p>
          </div>

          <div className="iframe-container">
            <h2>外层 iframe</h2>
            <div className="iframe-wrapper">
              <iframe
                id="outerIframe"
                srcDoc='
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>内层容器</title>
                    </head>
                    <body>
                        <div className="inner-container">
                            <h2>内层 iframe 容器</h2>

                            <div className="info">
                                <p>这个 iframe 中包含另一个使用 srcdoc 的 iframe。</p>
                            </div>

                            <div className="inner-iframe-wrapper">
                                <!-- 内层 iframe - 使用正确的转义 -->
                                <iframe id="innerIframe" src="http://localhost:5173/copilot/tool"></iframe>
                            </div>
                        </div>
                    </body>
                    </html>
                '
              ></iframe>
            </div>
          </div>

          <div className="description">
            <h3>修复的关键点</h3>
            <ol>
              <li>
                使用 <code>&amp;lt;</code> 和 <code>&amp;gt;</code> 替代{" "}
                <code>&lt;</code> 和 <code>&gt;</code>
              </li>
              <li>
                保持引号转义一致（使用 <code>&amp;quot;</code>）
              </li>
              <li>确保嵌套的 HTML 结构完整</li>
            </ol>

            <h3>工作原理</h3>
            <p>
              通过多层 <code>srcdoc</code> 属性嵌套，每层都使用正确的 HTML
              实体转义：
            </p>
            <pre>
              外层 iframe (srcdoc) └── 内层 iframe (srcdoc) └── 最内层内容
            </pre>
          </div>
        </div>
        <div style={{ textAlign: "right" }}>
          {current > 0 && (
            <Button
              onClick={() => setCurrent(current - 1)}
              style={{ marginRight: 8 }}
            >
              上一步
            </Button>
          )}
        </div>
      </Flex>
    </Flex>
  );
};

export default DraftPage;
// App.tsx
// import React from "react";

// const innerMostIframeContent = `
// <!DOCTYPE html>
// <html>
// <head>
//   <style>
//     body { font-family: Arial; padding: 20px; background: #e6f7ff; }
//     p { margin: 12px 0; }
//   </style>
// </head>
// <body>
//   <h4>最内层 iframe</h4>
//   <p>在这里可以划词测试，确保三层嵌套坐标正确。</p>
//   <p>再多写几行，保证有滚动条。</p>
//   <p>再多写几行，保证有滚动条。</p>
// </body>
// </html>
// `;

// const middleIframeContent = `
// <!DOCTYPE html>
// <html>
// <head>
//   <style>
//     body { font-family: Arial; padding: 20px; background: #fffbe6; }
//     .wrapper { border: 2px dashed red; padding: 10px; }
//   </style>
// </head>
// <body>
//   <h3>中间 iframe</h3>
//   <div class="wrapper">
//     <iframe
//       style="width:100%; height:300px; border:2px solid purple;"
//       srcdoc='${innerMostIframeContent
//         .replace(/\n/g, "")
//         .replace(/'/g, "&apos;")}'
//     ></iframe>
//   </div>
// </body>
// </html>
// `;

// const outerIframeContent = `
// <!DOCTYPE html>
// <html>
// <head>
//   <style>
//     body { font-family: Arial; padding: 20px; background: #f6ffed; }
//     .wrapper { border: 2px dashed green; padding: 10px; }
//   </style>
// </head>
// <body>
//   <h2>外层 iframe</h2>
//   <div class="wrapper">
//     <iframe
//       style="width:100%; height:400px; border:2px solid orange;"
//       srcdoc='${middleIframeContent.replace(/\n/g, "").replace(/'/g, "&apos;")}'
//     ></iframe>
//   </div>
// </body>
// </html>
// `;

// export default function App() {
//   return (
//     <div style={{ padding: 20 }}>
//       <h1>React 三层 iframe 嵌套示例</h1>
//       <iframe
//         style={{ width: "100%", height: "600px", border: "2px solid blue" }}
//         srcDoc={outerIframeContent}
//       />
//     </div>
//   );
// }
