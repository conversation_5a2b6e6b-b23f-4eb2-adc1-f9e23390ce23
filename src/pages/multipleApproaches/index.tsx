import React, { useRef, useState } from "react";
import {
  Layout,
  Button,
  Typography,
  Row,
  Col,
  message,
  Flex,
  theme,
  Spin,
  Form,
  Upload,
} from "antd";
import HeaderCom from "@/component/Header";
import { exportMarkdownToDocx, fileToBase64 } from "@/utils/common";
import uploadIcon from "@/assets/images/public/upload.png";
import { getToken, getUserInfo } from "@/utils/auth";
import StreamTypewriter from "@/component/StreamTypewriter";
import useSSEChat from "@/hooks/useSSEChat";
import "./index.less";
import { uploadChatFile } from "@/api/public";
import { DeleteOutlined } from "@ant-design/icons";
const { Content } = Layout;
const { Title } = Typography;
const { useToken } = theme;
const SmartQuestionGenerator = () => {
  const { token } = useToken();
  const sseChat = useSSEChat();
  const pageInfo = {
    pageName: "多解思路与解析",
    pageDesc: "拍照上传图片，多解法解析与对比，列误区与边界条件。",
    agentId: "dd7e337b-44c2-42b0-a803-fb4aa5579aee",
  };
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]); // 上传的文件信息
  const [streamingText, setStreamingText] = useState(""); // 流式的文本
  const scrollRef = useRef<HTMLDivElement>(null); // 滚动的dom
  const [globalLoading, setGlobalLoading] = useState(false); // 全局loading
  const handleStart = async () => {
    const fileData: any[] = [];
    uploadedFiles?.forEach((item: any) => {
      fileData.push({
        type: item.fileType || "image",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    setStreamingText("");
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: pageInfo.agentId || "",
        agentId: pageInfo.agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs: {},
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: "1",
          files: fileData,
        },
      },
      query: {},
      onMessage: (text: string) => {
        // 流式更新文本
        setStreamingText(text);
      },
      onFinished: (dataVal: any) => {
        setGlobalLoading(false);
      },
    });
  };

  // 上传文件
  const beforeUpload = async (file: File) => {
    const userInfo = await getUserInfo();
    const fileData = {
      fileName: file.name,
      fileStr: await fileToBase64(file),
      path: "/files/upload",
      agentId: pageInfo.agentId,
      user: userInfo?.id,
      libName: file.name,
      libDesc: "",
      flag: "file",
    };
    uploadChatFile(fileData).then(async (response: any) => {
      setGlobalLoading?.(false);
      if (response.code == 200) {
        setUploadedFiles([{ ...response.data }]);
        message.open({
          key: "uploading",
          type: "success",
          content: "文件上传成功",
          duration: 1,
        });
      } else {
        message.open({
          key: "uploading",
          type: "error",
          content: "文件上传失败",
          duration: 1,
        });
      }
    });
  };

  // 文件删除
  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };

  // 导出
  const handleExport = async () => {
    setGlobalLoading(true);
    const success = await exportMarkdownToDocx(streamingText, "试题.docx");

    if (success) {
      setGlobalLoading(false);
      message.success("导出成功");
    } else {
      setGlobalLoading(false);
      message.error("导出失败");
    }
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Layout
        style={{ minHeight: "100vh", background: "#fff" }}
        className="multiple-approaches"
      >
        <Content>
          <div className="set-question-header">
            {/* 顶部标题和描述 */}
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />

            <Row
              gutter={24}
              style={{
                width: streamingText ? "100%" : "800px",
                margin: "0px auto",
                padding: token.paddingMD,
              }}
            >
              {/* 左侧输入面板 */}
              <Col xs={24} md={streamingText ? 10 : 24}>
                <Flex
                  vertical
                  gap="middle"
                  style={{ flex: 1 }}
                  className="splitting-form"
                >
                  <Form layout="vertical">
                    <Form.Item>
                      <Flex className="upload">
                        <Flex vertical gap="4" style={{ flex: 1 }}>
                          <Title
                            level={4}
                            style={{
                              margin: 0,
                              display: "flex",
                              alignItems: "center",
                              marginTop: token.marginXXS,
                              marginBottom: token.marginMD,
                            }}
                          >
                            <div
                              style={{
                                width: 4,
                                height: 16,
                                background: "#1890ff",
                                marginRight: 8,
                                borderRadius: 2,
                              }}
                            ></div>
                            上传图片
                            <span
                              style={{
                                fontWeight: "normal",
                                fontSize: token.fontSize,
                              }}
                            >
                              {/* （成功识别25题） */}
                            </span>
                          </Title>
                          <Upload.Dragger
                            showUploadList={false}
                            multiple={false}
                            beforeUpload={beforeUpload}
                            accept=".png, .jpg, .gif, .webp, .svg"
                            fileList={uploadedFiles}
                          >
                            <img
                              src={uploadIcon}
                              style={{ width: 45, margin: "0px auto" }}
                            />
                            <p className="ant-upload-hint">
                              <span>点击或将文件拖到此处上传</span>
                              <span>支持图片格式</span>
                            </p>
                          </Upload.Dragger>
                          {uploadedFiles?.length > 0 && (
                            <div
                              className="file-list-contract"
                              style={{ margin: "12px 0" }}
                            >
                              {uploadedFiles.map((file) => (
                                <div
                                  key={file.id}
                                  className="file-item"
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 8,
                                  }}
                                >
                                  <span>{file.name}</span>
                                  <DeleteOutlined
                                    onClick={() => handleDelete(file.id)}
                                    style={{ cursor: "pointer" }}
                                  />
                                </div>
                              ))}
                            </div>
                          )}
                        </Flex>
                      </Flex>
                    </Form.Item>
                  </Form>
                  <div style={{ width: "100%", textAlign: "center" }}>
                    <Button
                      size="large"
                      type="primary"
                      className="upload-btn"
                      onClick={handleStart}
                      style={{
                        maxWidth: "500px",
                        width: "100%",
                      }}
                    >
                      开始
                    </Button>
                  </div>
                </Flex>
              </Col>

              {/* 右侧识别面板 - 条件渲染 */}
              {streamingText && (
                <Col xs={24} md={14}>
                  <Flex align="center" justify="space-between">
                    <Title
                      level={4}
                      style={{
                        margin: 0,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      <div
                        style={{
                          width: 4,
                          height: 16,
                          background: "#1890ff",
                          marginRight: 8,
                          borderRadius: 2,
                        }}
                      ></div>
                      识别区
                      <span
                        style={{
                          fontWeight: "normal",
                          fontSize: token.fontSize,
                        }}
                      >
                        {/* （成功识别25题） */}
                      </span>
                    </Title>
                    <Button type="primary" onClick={handleExport}>
                      导出试题
                    </Button>
                  </Flex>
                  <div
                    style={{
                      border: "1px solid #f0f0f0",
                      borderRadius: 8,
                      padding: 16,
                      background: "#fafafa",
                      height: "calc(100vh - 220px)",
                      overflow: "auto",
                      marginTop: token.marginMD,
                    }}
                    ref={scrollRef}
                  >
                    <StreamTypewriter
                      text={streamingText}
                      onchange={() => {
                        scrollRef.current?.scrollTo({
                          top: scrollRef.current.scrollHeight,
                          behavior: "smooth",
                        });
                      }}
                      end={true}
                      charsPerUpdate={5}
                    />
                  </div>
                </Col>
              )}
            </Row>
          </div>
        </Content>
      </Layout>
    </>
  );
};

export default SmartQuestionGenerator;
