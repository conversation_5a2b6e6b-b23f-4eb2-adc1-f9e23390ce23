import React, { useState, useRef, useEffect } from "react";
import Session from "@/component/Session";
import { Message } from "@/types/session";
import "./index.less";
import { Flex, Spin } from "antd";
import { getToken, getUserInfo } from "@/utils/auth";
import useSSEChat from "@/hooks/useSSEChat";
import dayjs from "dayjs";
const agentId = "13455053-e34d-417e-b0fd-30e664c93e71";

const IntelligentCustomer: React.FC = () => {
  const sessionRef = useRef(null);
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const sseChat = useSSEChat();
  const currentUser = "2"; //客户
  const [firstMessage, setFirstMessage] = useState(true);
  const [uploadFileList, setUploadFileList] = useState<any>([]);
  const [productList, setProductList] = useState<any>([]);
  const [markeDowanStatus, setMarkeDowanStatus] = useState<any>([]);
  const [messageList, setMessageList] = useState<Message[]>([
    // {
    //   id: "1",
    //   msg: "您好，请问信贷业务怎么办理",
    //   msgBelong: "2", //1-客服 2-客户
    //   time: "14:13",
    // },
    // {
    //   id: "2",
    //   msg: "您好！我是客服智能助手，您可以直接问我问题也可以转人工或者进行在线语音会议",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: true,
    // },
    // {
    //   id: "3",
    //   msg: "我司贷款业务包含",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "button",
    //     list: ["自营贷款", "委托贷款", "承兑贷款", "保理业务"],
    //   },
    // },
    // {
    //   id: "4",
    //   msg: "请再明确贷款用途",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "button",
    //     list: ["固定资产贷款", "流动资产贷款"],
    //   },
    // },
    // {
    //   id: "5",
    //   msg: "固定资产贷款是什么",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "button",
    //     list: ["上传材料", "相关产品推荐"],
    //   },
    // },
    // {
    //   id: "6",
    //   msg: "请上传以下材料",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "upload",
    //   },
    // },
    // {
    //   id: "7",
    //   msg: "您还需要填写以下表单",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "form",
    //   },
    // },
    // {
    //   id: "8",
    //   msg: "智能产品推荐",
    //   msgBelong: "1",
    //   time: "14:14",
    //   first: false,
    //   type: {
    //     type: "product",
    //   },
    // },
  ]);
  const [sessionBtnList] = useState([{ id: "1", name: "营销话术" }]);
  const getData = (name: string) => {
    const data: string = localStorage.getItem(name) as string;
    if (data) {
      console.log(data, "jsjsjsjsjj");
      return JSON.parse(data);
    } else {
      return [];
    }
  };
  useEffect(() => {
    setMessageList([...getData("messageList")]);
    setUploadFileList([...getData("uploadFileList")]);
    setMarkeDowanStatus([...getData("markeDowanStatus")]);
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      }, 100);
    }
  }, []);
  const submitMsg = async (msg: string) => {
    const inputs = {
      type: "智能问答助手",
    };
    messageList.push({
      id: generateRandom8Digits(),
      msg,
      msgBelong: "2",
      time: dayjs().format("HH:ss"),
    });
    let results = "";
    const res: any = await chatMessage(inputs, msg);
    if (firstMessage) {
      messageList.push({
        id: generateRandom8Digits(),
        msg: "您好！我是客服智能助手，您可以直接问我问题也可以转人工或者进行在线语音会议",
        msgBelong: "1",
        time: dayjs().format("HH:ss"),
        first: firstMessage,
      });
    }
    results += res;
    const cleanRes = results
      .replace(/```json\s*|```$/g, "")
      .trim()
      .replace(/```/g, "")
      .trim();
    console.log("ressjsjssk", cleanRes);
    const message = JSON.parse(cleanRes);
    if (Array.isArray(message)) {
      message.forEach((item: any) => {
        messageList.push({
          id: generateRandom8Digits(),
          msg: item.content,
          msgBelong: "1",
          time: dayjs().format("HH:ss"),
          type: {
            type: "button",
            list: item.button.split("，"),
          },
        });
      });
    } else {
      messageList.push({
        id: generateRandom8Digits(),
        msg: message.content,
        msgBelong: "1",
        time: dayjs().format("HH:ss"),
        type: {
          type: "button",
          list: message.button.split("，"),
        },
      });
    }

    setFirstMessage(false);
    setData("messageList", messageList);
    setData("messageListHis", messageList);
  };
  // 智能聊天-初始化
  const chatMessage = async (inputs: any, query: string) => {
    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    return new Promise((resolve) => {
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId || "",
          agentId: agentId || "",
          path: "/chat-messages",
          difyJson: {
            inputs,
            // pageinfo: pageInfo,
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query,
          },
        },
        query: {},
        onMessage: () => {},
        onFinished: (resultData: any) => {
          resolve(resultData); // 将结果传出去
          setGlobalLoading(false);
        },
      });
    });
  };
  // 随机生成8位数字
  const generateRandom8Digits = () => {
    // 生成一个0到99999999之间的随机数
    let randomNumber = Math.floor(Math.random() * 100000000).toString();

    // 将随机数转换为8位数字，前面不足8位的用0填充
    randomNumber = String(randomNumber).padStart(8, "0");

    return randomNumber;
  };
  // 点击消息上面的按钮
  const messageBtn = async (btnName: string) => {
    console.log(btnName, "jsjsjsj");
    if (btnName == "相关产品推荐") {
      const id = generateRandom8Digits();
      messageList.push({
        id,
        msg: "用户已选择" + btnName,
        msgBelong: "2",
        time: dayjs().format("HH:ss"),
      });
      setMessageList([...messageList]);
      getProductData(btnName);
      markeDowanStatus.push(id);
      setMarkeDowanStatus([...markeDowanStatus]);
      setData("markeDowanStatus", markeDowanStatus);
    } else {
      const inputs = {
        type: "智能问答助手",
      };
      messageList.push({
        id: generateRandom8Digits(),
        msg: "用户已选择" + btnName,
        msgBelong: "2",
        time: dayjs().format("HH:ss"),
      });
      let results = "";
      const res: any = await chatMessage(inputs, btnName);
      results += res;
      const cleanRes = results
        .replace(/```json\s*|```$/g, "")
        .trim()
        .replace(/```/g, "")
        .trim();
      console.log("ressjsjsskeeeeee", cleanRes);
      const message = JSON.parse(cleanRes);
      console.log("1111111111", message);

      if (Array.isArray(message)) {
        if (btnName == "上传审核文件") {
          message.forEach((item: any) => {
            if (item.module == "【文档上传组件】") {
              messageList.push({
                id: generateRandom8Digits(),
                msg: item.content,
                msgBelong: "1",
                time: dayjs().format("HH:ss"),
                type: {
                  type: "upload",
                },
              });
            }
            if (item.subassembly == "【贷款申请表】") {
              messageList.push({
                id: generateRandom8Digits(),
                msg: item.content,
                msgBelong: "1",
                time: dayjs().format("HH:ss"),
                type: {
                  type: "form",
                },
              });
            }
          });
        } else if (btnName == "固定资产贷款" || btnName == "流动资金贷款") {
          message.forEach((item: any) => {
            messageList.push({
              id: generateRandom8Digits(),
              msg:
                item.meaning + item.processing_flow + item.material_requested,
              msgBelong: "1",
              time: dayjs().format("HH:ss"),
              type: {
                type: "button",
                list: item.button && item.button.split("，"),
              },
            });
          });
        } else {
          message.forEach((item: any) => {
            messageList.push({
              id: generateRandom8Digits(),
              msg: item.content,
              msgBelong: "1",
              time: dayjs().format("HH:ss"),
              type: {
                type: "button",
                list: item.button && item.button.split("，"),
              },
            });
          });
        }
        setData("messageList", messageList);
        setData("messageListHis", messageList);
      } else {
        message.content.forEach((item: any) => {
          messageList.push({
            id: generateRandom8Digits(),
            msg: item.meaning + item.processing_flow + item.material_requested,
            msgBelong: "1",
            time: dayjs().format("HH:ss"),
            type: {
              type: "button",
              list: item.button && item.button.split("，"),
            },
          });
        });
      }
      setData("messageList", messageList);
      setData("messageListHis", messageList);
    }

    // }
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
  };
  // 上传文件
  const uploadFile = (file: any) => {
    console.log("cehsi", file);

    uploadFileList.push({
      ...file,
    });
    setUploadFileList([...uploadFileList]);
    setData("uploadFileList", uploadFileList);
    const id = generateRandom8Digits();
    messageList.push({
      id,
      msg: "用户已上传" + file.name,
      msgBelong: "2",
      time: dayjs().format("HH:ss"),
    });
    setMessageList([...messageList]);
    markeDowanStatus.push(id);
    setMarkeDowanStatus([...markeDowanStatus]);
    setData("markeDowanStatus", markeDowanStatus);
    setData("messageList", messageList);
    setData("messageListHis", messageList);
  };
  const setData = (name: string, data: any) => {
    localStorage.setItem(name, JSON.stringify(data));
  };
  // 提交表单
  const submitForm = () => {
    console.log("提交表单");
    const id = generateRandom8Digits();
    messageList.push({
      id,
      msg: "用户已提交表单",
      msgBelong: "2",
      time: dayjs().format("HH:ss"),
    });
    setMessageList([...messageList]);
    markeDowanStatus.push(id);
    setMarkeDowanStatus([...markeDowanStatus]);
    setData("markeDowanStatus", markeDowanStatus);
    // uploadFileList.push({
    //   created_at: 1757318793,
    //   created_by: "3ec04db0-ff97-45a2-810e-30407691622a",
    //   extension: "docx",
    //   id: "27d860b1-4893-496f-b358-24f377aab649",
    //   mime_type:
    //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    //   name: "企业贷款业务申请表.docx",
    //   size: 601517,
    //   uploadTime: "2025-09-09",
    // });
    console.log("jsjjsjsjj", uploadFileList);
    setData("messageList", messageList);
    setData("messageListHis", messageList);
    setData("uploadFileList", uploadFileList);
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
  };

  // 产品推荐
  const getProductData = async (btnName: string) => {
    const inputs = {
      type: "智能问答助手",
      // info: JSON.stringify(messageList),
    };
    // const inputs = {
    //   type: "产品推荐",
    //   info: JSON.stringify(messageList),
    // };
    let results = "";
    const res: any = await chatMessage(inputs, btnName);
    results += res;
    const cleanRes = results
      .replace(/```json\s*|```$/g, "")
      .trim()
      .replace(/```/g, "")
      .trim();
    console.log("999999999900088888", cleanRes);
    const message = JSON.parse(cleanRes);
    setProductList(message);
    const id = generateRandom8Digits();
    messageList.push({
      id,
      msg: "智能产品推荐",
      msgBelong: "1",
      time: dayjs().format("HH:ss"),
      type: {
        type: "product",
        productList: message,
      },
    });
    console.log("bbuussbn", messageList);
    setMessageList([...messageList]);
    markeDowanStatus.push(id);
    setMarkeDowanStatus([...markeDowanStatus]);
    setData("markeDowanStatus", markeDowanStatus);
    setData("messageList", messageList);
    setData("messageListHis", messageList);
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
    // }
  };
  // 全局loading
  const showLoading = (status: string) => {
    if (status == "1") {
      setGlobalLoading(true);
    } else if (status == "0") {
      setGlobalLoading(false);
    }
    if (sessionRef && sessionRef.current) {
      setTimeout(() => {
        console.log((sessionRef.current as any).scrollHeight, "ksksksk");
        (sessionRef.current as any).scrollTop = (
          sessionRef.current as any
        ).scrollHeight;
      });
    }
  };
  const deleteMessage = () => {
    setMessageList([]);
    setUploadFileList([]);
    setData("messageList", []);
    setData("uploadFileList", []);
    setData("markeDowanStatus", []);
  };
  const changeMarkeDownStatus = (id: string) => {
    markeDowanStatus.push(id);
    setMarkeDowanStatus([...markeDowanStatus]);
    setData("markeDowanStatus", markeDowanStatus);
  };
  return (
    <Flex className="intelligent-customer">
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Session
        sessionRef={sessionRef}
        className="session-box"
        messageList={messageList}
        submitMessage={submitMsg}
        sessionBtnList={sessionBtnList}
        currentUser={currentUser}
        messageBtn={messageBtn}
        uploadFile={uploadFile}
        submitForm={submitForm}
        productList={productList}
        showLoading={showLoading}
        deleteMessage={deleteMessage}
        markeDowanStatus={markeDowanStatus}
        changeMarkeDownStatus={changeMarkeDownStatus}
      ></Session>
    </Flex>
  );
};
export default IntelligentCustomer;
