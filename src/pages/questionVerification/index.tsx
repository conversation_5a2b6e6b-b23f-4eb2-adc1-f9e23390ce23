import React, {
  createRef,
  RefObject,
  Suspense,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Layout,
  Button,
  Row,
  Col,
  message,
  Flex,
  theme,
  Spin,
  Steps,
} from "antd";
import HeaderCom from "@/component/Header";
import { exportMarkdownToDocx } from "@/utils/common";
import MentionsFile from "@/component/MentionsFile/index";
import "./index.less";
import { RedoOutlined } from "@ant-design/icons";
const { Content } = Layout;
const { useToken } = theme;
const QuestionVerification = () => {
  const { token } = useToken();
  const [globalLoading, setGlobalLoading] = useState(false); // 全局loading
  const [current, setCurrent] = useState(-1); // 当前步骤
  const mentionsRef = useRef<any>({});
  const currentRef = useRef(current);
  const [pageInfo, setPageInfo] = useState<any>({
    pageName: "智能考题核验",
    pageDesc: "题目规范核查与知识点标注，涵盖措辞标点与考查层级评估。",
    pageOutPutData: {}, // 存储 startData
    steps: [
      {
        title: "错别字与用语核查",
        content: "<SpellCheckModal />",
        isExport: true,
        agentId: "86b701b3-adfd-4617-a41a-4d90f88fd4d9",
        unitOutPutData: {}, // 存储步骤0的输出数据
      },
      {
        title: "知识点覆盖与有效性",
        content: "<EffectivenessModal />",
        isExport: true,
        agentId: "1a898afe-4da0-4fc2-8aec-8dfe3c21d297",
        unitOutPutData: {}, // 存储步骤0的输出数据
      },
    ],
  });
  const currentStep = pageInfo.steps[current]; // 当前步骤信息

  // 自动匹配业务组件
  const modules = import.meta.glob("/src/businessComponents/**/index.tsx");
  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({});
  useEffect(() => {
    currentRef.current = current;
  }, [current]);

  // 懒加载当前组件
  const DynamicComponent = useMemo(() => {
    if (current < 0) return;
    const match = currentStep?.content?.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];

    if (!componentName) return null;

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
    const loader = modules[modulePath];

    if (!loader) {
      return null;
    }

    // 创建 ref（如果不存在）
    if (!refs.current[current]) {
      refs.current[current] = createRef();
    }

    const LazyComponent = React.lazy(loader as any);

    return (props: any) => (
      <LazyComponent ref={refs.current[current]} {...props} />
    );
  }, [current]);

  // 步骤条的点击
  const stepChange = async (value: number) => {
    if (pageInfo.steps[value]?.unitOutPutData) {
      const ref = refs.current[currentRef.current];
      const childData = await ref?.current?.triggerSplit?.();
      setPageInfo((prev) => {
        const newPageInfo = { ...prev };
        newPageInfo.steps[currentRef.current].unitOutPutData = {
          ...newPageInfo.steps[currentRef.current].unitOutPutData,
          ...childData,
        };
        return newPageInfo;
      });
      setCurrent(value);
    } else {
      message.warning("请完成当前步骤");
    }
  };

  // 点击上一步存储当前页面的数据
  const onPerv = async () => {
    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });
    if (currentRef.current > -1) {
      setCurrent(current - 1);
    }
  };

  // 清空后面的数据
  const clear = () => {
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      // 清空当前步骤及后续步骤的数据
      for (let i = currentRef.current; i < pageInfo.steps.length; i++) {
        if (newPageInfo.steps[i]) {
          // 保留基础数据，清空评审状态和生成的内容
          newPageInfo.steps[i].unitOutPutData = {};
        }
      }
      return newPageInfo;
    });
  };

  // 调用各个单元的提交.  重新生成
  const getSubmit = (type?: string) => {
    const ref = refs.current[currentRef.current];
    if (type == "1") {
      // 如果是1说明是重新生成，需要清空后面的值
      clear();
    }
    if (currentRef.current == 0) {
      ref?.current?.getSpellCheckData?.();
    } else if (currentRef.current == 1) {
      ref?.current?.getEffectivenessData?.();
    }
  };
  // 提交操作
  const getSubmitInfo = async () => {
    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });
    if (currentRef.current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
    }
  };
  // 开始
  const handleStart = () => {
    const data = mentionsRef.current.getMentionsData?.();
    const fileData: any[] = [];
    if (data?.localFile?.length < 1) {
      message.error("请上传文件");
      return;
    }
    data?.localFile?.forEach((item: any) => {
      fileData.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    setPageInfo((prev) => ({
      ...prev,
      pageOutPutData: {
        ...prev.pageOutPutData,
        originalFile: data?.originalFile, // 原始文件
        fileData: data?.localFile, // 上传后的文件数据
        queryData: data?.query,
      },
    }));
    setCurrent(0);
  };
  // 导出
  const exportBtn = async () => {
    setGlobalLoading(true);

    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();

    // 更新 pageInfo
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });

    // 直接从 childData 取，不依赖 pageInfo（避免异步更新问题）
    let str = "";
    if (currentRef.current === 0) {
      str = childData?.spellCheckData ?? "";
    } else if (currentRef.current === 1) {
      str = childData?.effectivenessData ?? "";
    }

    console.log("准备导出的 Markdown:", str);

    const success = await exportMarkdownToDocx(
      str,
      `${pageInfo.steps[currentRef.current].title}.docx`
    );

    if (success) {
      message.success("导出成功");
    } else {
      message.error("导出失败");
    }

    setGlobalLoading(false);
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex vertical className="question-verification-page">
        {/* 首页 */}
        {current < 0 ? (
          <>
            <div className="set-question-header">
              <HeaderCom
                mainTitle={pageInfo.pageName}
                subTitle={pageInfo.pageDesc}
              />
            </div>
            <Row
              gutter={24}
              style={{
                width: "800px",
                margin: "0px auto",
                padding: token.paddingMD,
              }}
            >
              {/* 左侧输入面板 */}
              <Col xs={24} md={24}>
                <MentionsFile
                  setGlobalLoading={setGlobalLoading}
                  ref={mentionsRef}
                  agentId={pageInfo?.steps[0]?.agentId}
                  placeholder="请输入题目信息"
                  onQueryChange={clear}
                  onFileChange={clear}
                  pageData={{
                    localFile: pageInfo?.pageOutPutData?.fileData,
                    originalFile: pageInfo?.pageOutPutData?.originalFile,
                    queryData: pageInfo?.pageOutPutData?.queryData,
                  }}
                />
                <Flex style={{ margin: "0px auto", maxWidth: "500px" }}>
                  <Button
                    type="primary"
                    size="large"
                    block
                    onClick={handleStart}
                    style={{ marginTop: token.marginLG }}
                  >
                    开始
                  </Button>
                </Flex>
              </Col>
            </Row>
          </>
        ) : (
          // 步骤页
          <Flex vertical className="legal-review-steps">
            <Flex className="info-con-steps" justify="center" align="center">
              <div className="content-title">
                <p>智能考题核验</p>
                <p>题目规范核查与知识点标注，涵盖措辞标点与考查层级评估。</p>
              </div>
              <Steps
                current={current}
                className="steps-con"
                onChange={stepChange}
              >
                {pageInfo.steps.map((item) => (
                  <Steps.Step key={item.title} title={item.title} />
                ))}
              </Steps>
            </Flex>
            <Flex vertical className="legal-page-con" justify="center">
              <div
                style={{
                  height: "calc(100vh - 148px)",
                }}
              >
                <Suspense
                  fallback={
                    <Content
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: "calc(100vh - 150px)", // 减去 header 高度
                      }}
                    >
                      {/* <Spin size="large" tip="加载中..." /> */}
                    </Content>
                  }
                >
                  {DynamicComponent ? (
                    <>
                      <div
                        style={{
                          display: current > -1 ? "block" : "none",
                        }}
                      >
                        <DynamicComponent
                          setGlobalLoading={setGlobalLoading}
                          agentId={currentStep.agentId}
                          unitOutPutData={
                            pageInfo?.steps[current]?.unitOutPutData
                          } // 父组件当前的值用于回显跟判断 有无输入输出
                          unitInputData={{
                            // 公共数据
                            fileData: pageInfo?.pageOutPutData?.fileData, // 文件信息
                            queryData: pageInfo?.pageOutPutData?.queryData, // 输入的数据
                            chunks:
                              pageInfo?.steps[0]?.unitOutPutData?.chunksData, // 拆分后的数据
                            // 第三步页面用的数据
                            ruleFrom:
                              pageInfo?.steps[1]?.unitOutPutData?.ruleFormData,
                            // 最后一步页面用的数据
                            targentData:
                              pageInfo?.steps[2]?.unitOutPutData?.targentData, // 目标的数据
                            rulesData:
                              pageInfo?.steps[2]?.unitOutPutData?.rulesData,
                            originalFile:
                              pageInfo?.pageOutPutData?.originalFile,
                            selectedRules:
                              pageInfo?.steps[2]?.unitOutPutData?.selectedRules,
                          }}
                          pageInfo={{
                            pageName: pageInfo.pageName,
                            pageDesc: pageInfo.pageDesc,
                            pageInputDesc: pageInfo.pageInputDesc,
                            pageOutputDesc: pageInfo.pageOutputDesc,
                            steps: [pageInfo?.steps[currentRef.current]],
                          }}
                        />
                      </div>
                    </>
                  ) : (
                    <div>组件未找到</div>
                  )}
                </Suspense>
              </div>

              <Flex
                justify="center"
                align="center"
                gap={token.marginMD}
                className="legal-review-footer"
              >
                <Button
                  style={{ minWidth: "100px", marginRight: 8 }}
                  onClick={async () => {
                    const ref = refs.current[currentRef.current];
                    const childData = await ref?.current?.triggerSplit?.();
                    setPageInfo((prev) => {
                      const newPageInfo = { ...prev };
                      newPageInfo.steps[currentRef.current].unitOutPutData = {
                        ...newPageInfo.steps[currentRef.current].unitOutPutData,
                        ...childData,
                      };
                      return newPageInfo;
                    });
                    setCurrent(-1);
                  }}
                >
                  返回首页
                </Button>

                {current > 0 && (
                  <Button
                    style={{ minWidth: "100px", marginRight: 8 }}
                    onClick={onPerv}
                  >
                    上一步
                  </Button>
                )}
                <Button
                  icon={<RedoOutlined />}
                  onClick={() => {
                    getSubmit("1");
                  }}
                >
                  重新生成
                </Button>
                {current < pageInfo.steps.length - 1 && (
                  <Button
                    type="primary"
                    onClick={getSubmitInfo}
                    style={{ minWidth: "100px", marginRight: 8 }}
                  >
                    {currentStep?.submitName || "下一步"}
                  </Button>
                )}
                {currentStep?.isExport && (
                  <Button type="primary" onClick={exportBtn}>
                    导出
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default QuestionVerification;
