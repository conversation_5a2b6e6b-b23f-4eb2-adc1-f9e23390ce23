@import "@/assets/styles/variables.less";
.legal-review-page {
  background: @background;
  min-height: 100vh;
  .legal-review-home {
    .splitting-form {
      width: 800px;
      padding: 20px 50px;
      border-radius: var(--ant-border-radius-lg);
      border: 1px solid #eeeeee;
      margin: 0px auto;
      margin-top: 50px;
      .ant-select-selector {
        border-color: #eeeeee;
      }
      .upload-btn {
        width: 120px;
        margin: 0px auto;
        background: @linear-gradient-1;
      }
    }
  }
  // 上传文件样式

  .ant-upload-drag {
    border: 1px dashed #e0eaff;
    border-radius: 12px;
    padding: 40px 0px;
    background: #ffffff;
  }
  .ant-card-body {
    box-shadow: 0px 0px 20px 0px rgba(89, 143, 200, 0.05);
    border-radius: 12px;
    padding: 30px;
  }
  .file-title {
    font-family: Alibaba PuHuiTi 3;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
    margin-bottom: 20px;
  }
  .ant-upload-drag-icon {
    margin-bottom: 0;
    font-size: 36px;
    color: #1890ff;
    transition: transform 0.3s ease;
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
    span:nth-child(1) {
      display: block;
      font-family: Alibaba PuHuiTi 3;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: #333333;
    }
    span:nth-child(2) {
      display: block;
      opacity: 0.6;
      font-family: Alibaba PuHuiTi 3;
      font-size: 12px;
      font-weight: normal;
      margin-top: 5px;
    }
  }
  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12px;
      }
    }
  }
  .file-item-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .info-con-steps {
    height: 79px;
    position: relative;
    padding: 0px 30px 0px 50x;
    // background: var(--ant-color-bg-container);
    // border-bottom: 1px solid var(--ant-color-border);
    .steps-con {
      width: 900px;
      margin: 0px auto;
    }
    .content-title {
      position: absolute;
      left: 50px;
      // top: 16px;
      p:nth-child(1) {
        font-size: 18px;
        line-height: 26px;
        font-weight: 500;
        color: #333333;
      }
      p:nth-child(2) {
        font-size: var(--ant-font-size-sm);
        color: #333333;
        margin-top: 6px;
        line-height: var(--line-height-sm);
      }
    }
  }
  .info-con-steps-ot {
    background: var(--ant-color-bg-container);
    border-bottom: 1px solid var(--ant-color-border);
  }
  .legal-review-footer {
    width: 100%;
    height: 70px;
    background: var(--ant-color-bg-container);
    position: fixed;
    left: 0px;
    bottom: 0px;
    .legal-fixed-bottom {
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      position: absolute;
      right: 20px;
      top: 20px;
      color: #ff4d4f;
    }
  }
}
.knowledge-modal {
  margin-bottom: 24px;
  padding: 0 10px;
  .ant-card {
    flex: 1;
    border-radius: 8px;
    cursor: pointer;
  }
  .knowledge-item {
    background-color: rgba(147, 210, 243, 0.3);
    color: rgba(64, 149, 229, 1);
    fill: rgba(64, 149, 229, 1);
    font-weight: 500;
  }
}
.knowledge-list {
  display: flex;
  justify-content: start;
  margin-bottom: 8px;
  font-size: 16px;
  span {
    // flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    // margin-right: 12px;
  }
}
.knowledge-list-active {
  color: rgba(64, 149, 229, 1);
  fill: rgba(64, 149, 229, 1);
  background-color: rgba(147, 210, 243, 0.3);
}
.legal-page-library {
  margin: auto;
  margin-top: 40px;
  width: 1090px;
}
.file-list {
  // margin-top: 24px;
  max-height: 258px;
  overflow-y: auto;
  padding-right: 4px;
  flex: 1; // 让文件列表占据剩余空间
  .section-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    // background: #f8f9fa;
    background: #fff;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    .file-item-name {
      // flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
    .file-item-icon {
      display: flex;
      .file-item-ledge {
        display: flex;
        margin-right: 20px;
        color: #1890ff;
      }
    }
  }

  .file-item-ot {
    border: 1px solid #eeeeee;
    .file-item-ot-pop {
      display: flex;
    }
    .file-item-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .file-item-name {
      cursor: pointer;
      font-size: 14px;
      max-width: 450px;
    }
  }
}

.knowledge-content {
  width: 500px;
  .knowledge-content-tags {
    margin-right: 8px;
    padding: 4px 10px;
    background: rgba(147, 210, 243, 0.3);
    color: #1890ff;
    border-radius: 8px;
    font-size: 14px;
    line-height: 14px;
  }
}
.legal-page-completed {
  padding: 50px 130px;
  width: 966px;
  height: calc(100vh - 230px);
  min-height: 728px;

  .ant-card-body {
    display: flex;
    flex-direction: column;
    box-shadow: none;
    width: 100%;
    height: 100%;
  }
  .completed-upload {
    width: 100%;
  }
  .file-list-completed {
    margin-top: 20px;
    max-height: 368px;
    .file-item {
      justify-content: start;
      background: #f0f2f5;
      .file-item-title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .completed-btn {
    margin-top: auto;
  }
}
.file-split {
  height: 100%;
  .file-split-left {
    margin-right: 40px;
    padding: 30px 80px;
    background-color: #fff;
    width: 500px;
    height: 100%;
  }
  .file-split-right {
    flex: 1;
    padding: 20px;
    background-color: #fff;
  }
}
.icon-font-margin {
  margin-right: 6px;
  max-width: none;
  // font-size: 16px;
}
.upload-btn {
  // width: 120px;
  margin: 0px auto;
  background: @linear-gradient-1 !important;
  color: #fff !important;
}
.split-card {
  padding: 10px 20px 20px;
  height: 190px;
  .split-card-detail {
    padding: 0px;
  }
  .ant-card-head {
    padding: 0px;
    min-height: 42px !important;
    font-size: var(--ant-font-size-lg);
    font-weight: bold;
    line-height: 22px;
    color: #333333;
    border-bottom: 0px;
  }
  .ant-card-body {
    padding: 0px;
    margin-top: var(--ant-margin-xs);
    font-size: var(--ant-font-size);
    line-height: var(--ant-line-height);
    color: #333333;
  }
  &:hover {
    border: 1px solid var(--ant-color-primary);
  }
}

.file-item-active {
  background: rgba(147, 210, 243, 0.3) !important;
}
.file-split-right-card {
  margin-bottom: 24px;
}
.split-setting-title {
  margin-bottom: 10px;
}
.icon-font-finish {
  margin-right: 24px;
  font-size: 50px;
}

.split-show-item {
  margin-left: -8px;
  margin-right: -8px;
  flex-flow: row wrap;
  max-height: calc(100% - 218px);
  overflow-y: scroll;
  .split-show-item-col {
    padding: 8px;
  }
}
.knowledge-list-modal {
  font-size: 14px;
  .knowledge-list-modal-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.knowledge-content-tags-tit {
  display: flex;
  flex-wrap: wrap;
  .knowledge-content-tags {
    margin-bottom: 10px;
    width: max-content;
    line-height: 25px;
  }
}
.preview-modal {
  .ant-modal-content {
    min-height: 500px;
    max-height: 70vh;
    overflow-y: auto;
  }
}
