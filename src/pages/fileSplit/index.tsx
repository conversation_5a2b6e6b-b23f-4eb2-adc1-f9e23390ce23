// DraftPage.tsx
import React, { useState, useRef, useEffect } from "react";
import {
  Button,
  Flex,
  Form,
  message,
  Upload,
  Spin,
  Typography,
  theme,
  Steps,
  Modal,
  Card,
  Input,
  Popconfirm,
  Popover,
  Col,
  Table,
  InputNumber,
} from "antd";
import HeaderCom from "@/component/Header";
import { uploadCommonFile } from "@/api/public";
import IconFont from "@/component/IconFont";
import "./index.less";
import {
  DeleteOutlined,
  CheckCircleFilled,
  InboxOutlined,
  SearchOutlined,
  EditOutlined,
  StopOutlined,
  ReadOutlined,
} from "@ant-design/icons";
import {
  getKnowledgeList,
  getKnowledgeDetail,
  intelligentStorage,
  previewFile,
  addNotSplit,
  syncSplit,
  addConSplitPar,
} from "@/api/fileSplit";
import {
  KnowledgeInfo,
  NotSplitList,
  SyncSplit,
  SyncSplitItem,
  SyncSplitListResItem,
  UploadedFilesItem,
} from "@/types/fileSplit";
import { KnowledgeListRes } from "@/types/fileSplit";
const { useToken } = theme;

const DraftPage: React.FC = () => {
  const { token } = useToken();
  const [pageInfo] = useState<any>({
    pageName: "智能文件拆分助手",
    pageDesc:
      "欢迎使用智能文件拆分助手复杂文件智能识别 + 自定义规则，帮你快速拆出想要的内容",
    pageInputDesc: "用户上传需要进行法审的合同内容",
    pageOutputDesc: "含有批注内容的word文档",
    steps: [
      {
        title: "文本上传",
        content: "<SplitviewModule />",
        submitName: "",
        isExport: false,
        agentId: "",
      },
      {
        title: "文件入库",
        content: "<FileLibraryModule />",
        submitName: "",
        isExport: false,
        agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
        inputSource: "文档切分，场景规划师",
      },
      {
        title: "文件拆分",
        content: "<TargetingModule />",
        submitName: "",
        isExport: false,
        agentId: "a5f3cb88-d63b-417c-ab81-90c1307e2c31",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
      },
      {
        title: "处理并完成",
        content: "<ContractLawReview />",
        submitName: "",
        isExport: true,
        agentId: "06d5b6f0-fdd1-4f7c-91d7-4f73c5f89ad1",
        inputDesc: "用户上传合同内容和规则条例",
        outputDesc: "输出合同校验结果",
        inputSource: "规则匹配",
      },
    ],
  });
  const [current, setCurrent] = useState(0);
  // 2️⃣ 用 ref 始终保存最新的 current 值（避免闭包问题）
  const currentRef = useRef(current);
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const [uploadedFiles, setUploadedFiles] = useState([]); // 上传的文件信息
  // const [originalFile, setOriginalFile] = useState<any>(null); // 原始上传的文件
  const currentStep = pageInfo.steps[current]; // 当前步骤信息
  const [isModalOpen, setIsModalOpen] = useState(false); // 选择入库方式弹窗

  useEffect(() => {
    currentRef.current = current;
  }, [current]);

  // 提交操作
  const getSubmitInfo = async () => {
    if (currentRef.current < pageInfo.steps.length - 1) {
      // setCurrent(current + 1);
      if (current == 1) {
        setIsSplitModalOpen(true);
      } else if (current == 2) {
        getAddConSplitPar();
      }
    }
  };

  // 点击上一步存储当前页面的数据
  const onPerv = async () => {
    if (currentRef.current > -1) {
      if (splitTypes == "4") {
        setCurrent(current - 2);
      } else {
        setCurrent(current - 1);
      }
    }
  };

  // 上传文件
  const beforeUpload = async (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    setGlobalLoading?.(true);
    if (["docx", "doc", "pdf", "txt", "md"].includes(originalFileExt)) {
      uploadCommonFile(file)
        .then(async (response: any) => {
          if (response.code == 200) {
            setUploadedFiles((prevFiles) => {
              response.data.title = file.name;
              return [...prevFiles, response.data] as any;
            });
            message.open({
              key: "uploading",
              type: "success",
              content: "文件上传成功",
              duration: 1,
            });
          } else {
            message.open({
              key: "uploading",
              type: "error",
              content: "文件上传失败",
              duration: 1,
            });
          }
        })
        .finally(() => {
          setGlobalLoading?.(false);
        });
    }
  };
  // 首页删除
  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file: any) => file.fileId !== fileId)
    );
    setSyncSplitList((prevFiles: any) =>
      prevFiles.filter((file: any) => file.fileId !== fileId)
    );
  };
  // 首页点击下一步
  const nextContent = async () => {
    if (uploadedFiles.length === 0) {
      message.error("请先上传文件");
      return;
    }
    showModal();
    getKnowledgeData();
  };
  // 首页选择入库方式弹窗
  const showModal = () => {
    setIsModalOpen(true);
  };

  // 首页选择入库方式后点击确定
  const handleOk = () => {
    if (!knowledgeType) {
      message.error("请选择入库方式");
    } else {
      if (knowledgeType == "2") {
        if (!currentId) {
          return message.error("请选择知识库");
        } else {
          const data: any = uploadedFiles.map((item: any) => {
            item.libId = currentId;
            item.libName = currentName;
            return item;
          });
          setUploadedFiles(data);
        }
      } else {
        const files = uploadedFiles.map((item: UploadedFilesItem) => {
          return item.fileId;
        });
        getIntelligentStorage(files);
      }
      setIsModalOpen(false);
      setCurrent(1);
      setCurrentId("");
      setKnowledgeTypeValue("");
      setKnowledgeType("");
    }
  };
  // 首页选择入库方式后点击取消
  const handleCancel = () => {
    setIsModalOpen(false);
    setKnowledgeType("");
    setCurrentId("");
    setKnowledgeTypeValue("");
  };

  // 首页选择知识库
  const [knowledgeType, setKnowledgeType] = useState(""); // 入库方式
  const [knowledgeTypeChild, setKnowledgeTypeChild] = useState(""); // 第二步入库方式
  const [knowledgeDefaultList, setKnowledgeDefaultList] = useState([]); // 知识库默认参数
  const [knowledgeList, setKnowledgeList] = useState<KnowledgeListRes>([]); // 知识库列表
  const [isKnowledgeModalOpen, setIsKnowledgeModalOpen] = useState(false); // 选择入库方式弹窗
  const [currentId, setCurrentId] = useState(""); // 知识库id
  const [currentName, setCurrentName] = useState(""); // 知识库名字
  const [isSplitModalOpen, setIsSplitModalOpen] = useState(false); //选择拆分方式-弹窗
  const [isSplitTypeOpen, setIsSplitTypeOpen] = useState(false); // 拆分设置-弹窗
  const [splitTypes, setSplitTypes] = useState(""); // 选择拆分方式
  const [splitTypesChild, setSplitTypesChild] = useState(""); //第三步展示拆分方式
  const [knowledgeTypeValue, setKnowledgeTypeValue] = useState(""); // 选择知识库弹窗-搜索知识库
  const [libraryIpt, setLibraryIpt] = useState(""); // 编辑知识库-搜索知识库
  const [previewFileUrl, setPreviewFileUrl] = useState(""); // 第二步-文件预览地址
  const [syncSplitList, setSyncSplitList] = useState<any>([]); // 第三步-文件拆分-文件列表
  const [selectFileId, setSelectFileId] = useState(""); //文件拆分-选择文件id
  const [editId, setEditId] = useState(""); //文件拆分-编辑文件id
  const [splitEditData, setSplitEditData] = useState<SyncSplitListResItem>(
    {} as SyncSplitListResItem
  ); // 拆分文件-当前选中文件信息
  const [selectFileContent, setSelectFileContent] =
    useState<SyncSplitListResItem>({} as SyncSplitListResItem); // 文件拆分-右侧展示内容
  const [knowledgeInfo, setKnowledgeInfo] = useState<KnowledgeInfo>(
    {} as KnowledgeInfo
  ); // 拆分文件-知识库详情
  const [previewModal, setPreviewModal] = useState<{
    open: boolean;
    title: string;
    content: string;
  }>({ open: false, title: "", content: "" }); //拆分文件-右侧查看详情按钮
  const selectSplit = (val: string) => {
    setSplitTypes(val);
    setSplitTypesChild(val);
  };
  //选择入库方式弹窗-选中入库方式
  const selectKnowledgeType = (val: string) => {
    setKnowledgeType(val);
    setKnowledgeTypeChild(val);
  };
  // 文件入库-打开编辑知识库弹窗
  const showKnowledgeModal = () => {
    setIsKnowledgeModalOpen(true);
  };
  //选择知识库弹窗-点击确定按钮
  const handleKnowledgeOk = () => {
    setIsKnowledgeModalOpen(false);
    setCurrentId("");
    setLibraryIpt("");
    const data: any = uploadedFiles.map((item: any) => {
      if (item.fileId == editId) {
        item.libId = currentId;
        item.libName = currentName;
      }
      return item;
    });
    setUploadedFiles(data);
  };
  //选择知识库弹窗-点击取消按钮
  const handleKnowledgeCancel = () => {
    setIsKnowledgeModalOpen(false);
    setCurrentId("");
    setLibraryIpt("");
    searchLibraryIpt({
      target: {
        value: "",
      },
    });
  };
  // 文件入库-编辑按钮
  const handleEdit = (id: string) => {
    setEditId(id);
    showKnowledgeModal();
  };
  // 选择知识库弹窗-人工选择-选择知识
  const selectKnowledge = (item: any) => {
    console.log(item);
    setCurrentId(item.id);
    setCurrentName(item.libName);
  };
  //文件入库-知识库详情-表格表头
  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 50,
    },
    {
      title: "文件名称",
      dataIndex: "title",
      key: "title",
      ellipsis: true,
    },
    {
      title: "字符数",
      dataIndex: "fileSize",
      key: "fileSize",
    },
    {
      title: "上传时间",
      dataIndex: "uploadTime",
      key: "uploadTime",
      ellipsis: true,
    },
  ];
  // 文件入库-知识库详情-内容
  const knowledgeContent = () => {
    return (
      <Flex vertical className="knowledge-content">
        <Typography.Title level={4}>{knowledgeInfo.libName}</Typography.Title>
        <Typography.Title level={5} style={{ marginTop: "0px" }}>
          描述：
        </Typography.Title>
        <Typography.Text style={{ marginBottom: "15px" }}>
          {knowledgeInfo.libDesc}
        </Typography.Text>
        <Typography.Title level={5} style={{ marginTop: "0px" }}>
          标签：
        </Typography.Title>
        <Typography.Text
          style={{ marginBottom: "5px" }}
          className="knowledge-content-tags-tit"
        >
          {knowledgeInfo &&
            knowledgeInfo.tags &&
            knowledgeInfo.tags.split(",").map((item, index) => (
              <span key={index} className="knowledge-content-tags">
                {item}
              </span>
            ))}
        </Typography.Text>
        <Typography.Title level={5} style={{ marginTop: "0px" }}>
          文档：
        </Typography.Title>
        <Typography.Text>
          <Table
            dataSource={knowledgeInfo.knInfoList}
            columns={columns}
            pagination={{ position: ["none", "none"] }}
            size="small"
            scroll={{ y: 40 * 5 }}
          />
        </Typography.Text>
      </Flex>
    );
  };
  // 拆分弹窗-点击确定（下一步）
  const handleSplitOk = () => {
    if (!splitTypes) {
      return message.error("请选择拆分方式");
    }
    if (uploadedFiles && uploadedFiles.length == 0) {
      return message.error("知识列表不可为空");
    }
    // 不拆分-点击下一步
    if (splitTypes == "4") {
      setGlobalLoading?.(true);
      const notSplitList: NotSplitList = uploadedFiles.map(
        (item: UploadedFilesItem) => {
          return {
            libId: item.libId,
            fileId: item.fileId,
            title: item.title,
          };
        }
      );
      setIsSplitModalOpen(false);
      addNotSplit(notSplitList)
        .then((res) => {
          if (res.code == 200) {
            setCurrent(current + 2);
            setSplitTypes("");
          } else {
            message.error(res.msg);
            setSplitTypes("");
          }
        })
        .finally(() => {
          setGlobalLoading?.(false);
          setSplitTypes("");
        });
    } else {
      // 拆分-点击下一步
      setGlobalLoading?.(true);
      const splitList: SyncSplit = uploadedFiles.map((item: SyncSplitItem) => {
        return {
          libId: item.libId,
          fileId: item.fileId,
          title: item.title,
        };
      });
      setIsSplitModalOpen(false);
      syncSplit(splitList)
        .then((res) => {
          if (res.code == 200) {
            setSyncSplitList(res.data);
            setSelectFileId(res.data[0].fileId);
            setSelectFileContent(res.data[0]);
            setCurrent(current + 1);
            setSplitTypes("");
          } else {
            message.error(res.msg);
          }
        })
        .finally(() => {
          setGlobalLoading?.(false);
          setSplitTypes("");
        });
    }
  };
  // 拆分弹窗-点击取消
  const handleSplitCancel = () => {
    setIsSplitModalOpen(false);
    // setKnowledgeType("");
    setSplitTypes("");
  };
  // 知识库列表
  const getKnowledgeData = () => {
    getKnowledgeList({
      libName: "",
    }).then((res) => {
      if (res.code === 200) {
        // setKnowledgeDataData(res.data.records);
        setKnowledgeList(res.data);
        setKnowledgeDefaultList(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };
  // 搜索知识库
  const searchKnowledge = (val: any) => {
    setKnowledgeTypeValue(val.target.value);
    if (val.target.value == "") {
      setKnowledgeList(knowledgeDefaultList);
    } else {
      setKnowledgeList(() =>
        knowledgeDefaultList.filter((item: any) =>
          item.libName.includes(val.target.value)
        )
      );
    }
  };
  // 选择知识库弹窗-搜索知识库
  const searchLibraryIpt = (val: any) => {
    setLibraryIpt(val.target.value);
    if (val.target.value == "") {
      setKnowledgeList(knowledgeDefaultList);
    } else {
      setKnowledgeList(() =>
        knowledgeDefaultList.filter((item: any) =>
          item.libName.includes(val.target.value)
        )
      );
    }
  };
  // 知识库详情弹窗
  const openKnowledgeContent = (isOpen: boolean, id: string) => {
    if (isOpen) {
      getKnowledgeInfo(id);
    }
  };
  // 获取知识库详情
  const getKnowledgeInfo = (id: string) => {
    getKnowledgeDetail(id).then((res) => {
      if (res.code === 200) {
        // setKnowledgeDataData(res.data.records);
        const temp = JSON.parse(JSON.stringify(res.data));
        const newData = temp.knInfoList.map((item: any, index: number) => {
          item.index = index + 1;
          item.fileSize = item.fileSize.toFixed(2) + "MB";
          return item;
        });
        temp.knInfoList = newData;
        setKnowledgeInfo(temp);
      } else {
        message.error(res.msg);
      }
    });
  };
  // 智能入库接口
  const getIntelligentStorage = (files: string[]) => {
    setGlobalLoading(true);
    intelligentStorage(files)
      .then((res) => {
        if (res.code === 200) {
          setUploadedFiles(res.data);
        } else {
          message.error(res.msg);
          setCurrent(0);
        }
      })
      .catch(() => {
        setCurrent(0);
      })
      .finally(() => {
        setGlobalLoading(false);
      });
  };

  // 文件预览弹窗
  const openFileName = (isOpen: boolean, id: string) => {
    if (isOpen) {
      previewFile(id).then((res) => {
        if (res.code === 200) {
          setPreviewFileUrl(res.data);
        } else {
          message.error(res.msg);
        }
      });
    }
  };

  // 判断使用哪个图标
  const formatIcon = (fileName: string) => {
    const type = fileName.split(".")[fileName.split(".").length - 1];
    if (type === "pdf") {
      return "pdf";
    } else if (type === "doc" || type === "docx") {
      return "word";
    } else if (type === "txt") {
      return "txt";
    } else if (type === "md") {
      return "md";
    } else {
      return "";
    }
  };

  // 拆分文件-选择文件
  const selectFile = (file: any) => {
    setSelectFileId(file.fileId);
    setSelectFileContent(file);
  };

  // 知识新增（包含拆分段落）
  const getAddConSplitPar = () => {
    setGlobalLoading?.(true);
    addConSplitPar(syncSplitList)
      .then((res) => {
        if (res.code == 200) {
          setCurrent(current + 1);
        } else {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setGlobalLoading?.(false);
      });
  };
  // 拆分类型
  const formatSplitType = (type: string) => {
    if (type == "normal") {
      return "正常";
    } else if (type == "parentChild") {
      return "父子";
    }
  };

  // 拆分文件页面弹窗
  const handleSplitEdit = (item: SyncSplitListResItem) => {
    setIsSplitTypeOpen(true);
    setSplitEditData(item);
    setSegmentationMarker(item.chunkSegmentString);
    setChunkMaxLength(item.chunkMaxLength);
    setChunkOverLapLength(item.chunkOverLapLength);
    setFileSplitType(
      item.splitType == "normal"
        ? "普通"
        : item.splitType == "parentChild"
        ? "父子"
        : ""
    );
  };
  //拆分设置-取消
  const handleSplitTypeCancel = () => {
    setIsSplitTypeOpen(false);
  };
  //拆分设置-确定
  const handleSplitTypeOk = () => {
    setGlobalLoading?.(true);
    const arr: any = [];
    arr.push(splitEditData);
    const splitList: SyncSplit = arr.map((item: SyncSplitItem) => {
      return {
        libId: item.libId,
        fileId: item.fileId,
        title: item.title,
        chunkSegmentString: segmentationMarker,
        chunkMaxLength: chunkMaxLength,
        chunkOverLapLength: chunkOverLapLength,
      };
    });
    setIsSplitTypeOpen(false);
    syncSplit(splitList)
      .then((res) => {
        if (res.code == 200) {
          const temp = syncSplitList.map((item: any) => {
            if (item.fileId == res.data[0].fileId) {
              item = res.data[0];
            }
            return item;
          });
          setSyncSplitList(temp);
          setSelectFileContent(res.data[0]);
        }
      })
      .finally(() => {
        setGlobalLoading?.(false);
        // setSplitTypes("");
      });
  };

  const [fileSplitType, setFileSplitType] = useState(""); // 分段标识
  const [segmentationMarker, setSegmentationMarker] = useState(""); // 分段标识
  const [chunkMaxLength, setChunkMaxLength] = useState(0); // 分段最大长度
  const [chunkOverLapLength, setChunkOverLapLength] = useState(0); // 分段重叠长度
  // 修改分段标识符
  const changeChunkSegmentString = (e: any) => {
    setSegmentationMarker(e.target.value);
  };
  // 修改分段最大长度
  const changeChunkMaxLength = (value: any) => {
    setChunkMaxLength(value);
  };
  // 修改分段重叠长度
  const changeChunkOverLapLength = (value: any) => {
    setChunkOverLapLength(value);
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex vertical className="legal-review-page">
        {/* 首页 */}
        {current == 0 ? (
          <Flex vertical className="legal-review-home">
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />
            <Flex vertical>
              {/* <Flex className="info-con-steps" justify="center" align="center">
                <div className="content-title">
                  <p>智能合同审查助手</p>
                  <p>法审、敏感词、批注</p>
                </div>
                <Steps current={current} className="steps-con">
                  {pageInfo.steps.map((item: any) => (
                    <Steps.Step key={item.title} title={item.title} />
                  ))}
                </Steps>
              </Flex> */}
              <Flex
                vertical
                gap="middle"
                style={{ flex: 1 }}
                className="splitting-form"
              >
                <Form layout="vertical">
                  <Form.Item>
                    <Flex className="upload">
                      <Flex vertical gap="4" style={{ flex: 1 }}>
                        <Typography.Title className="file-title" level={5}>
                          文件上传
                        </Typography.Title>
                        <Upload.Dragger
                          showUploadList={false}
                          multiple={true}
                          beforeUpload={beforeUpload}
                          accept=".docx,.doc,.pdf,.txt,.md"
                          fileList={uploadedFiles}
                        >
                          {/* <img
                            src={uploadIcon}
                            style={{ width: 45, margin: "0px auto" }}
                          /> */}
                          <div className="ant-upload-drag-icon">
                            {uploadedFiles.length > 0 ? (
                              <CheckCircleFilled />
                            ) : (
                              <InboxOutlined />
                            )}
                          </div>
                          <p className="ant-upload-hint">
                            {/* {uploadedFiles && uploadedFiles.length > 0 ? (
                              <span>{uploadedFiles[0].name}</span>
                            ) : (
                              <span>点击或将文件拖到此处上传</span>
                            )} */}
                            <span>点击或将文件拖到此处上传</span>
                            <span>
                              支持pdf,doc,docx,txt,md格式文件，可上传多个文本文件
                            </span>
                          </p>
                        </Upload.Dragger>
                      </Flex>
                    </Flex>
                  </Form.Item>
                  {/* 文件列表 */}
                  {uploadedFiles.length > 0 && (
                    <div className="file-list">
                      <Typography.Text
                        className="section-title"
                        style={{ display: "block" }}
                      >
                        文档列表
                      </Typography.Text>
                      {uploadedFiles.map((file: any) => (
                        <div key={file.fileId} className="file-item">
                          <span className="file-item-title">{file.title}</span>
                          <DeleteOutlined
                            onClick={() => handleDelete(file.fileId)}
                            style={{
                              color: "#ff4d4f",
                              cursor: "pointer",
                              fontSize: "14px",
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </Form>
                <Button
                  size="large"
                  type="primary"
                  className="upload-btn"
                  onClick={nextContent}
                >
                  下一步
                </Button>
              </Flex>
            </Flex>
          </Flex>
        ) : (
          // 步骤页
          <Flex vertical className="legal-review-steps">
            <Flex
              className="info-con-steps info-con-steps-ot"
              justify="center"
              align="center"
            >
              <div className="content-title">
                <p>文件知识入库</p>
              </div>
              <Steps current={current} className="steps-con">
                {pageInfo.steps.map((item: any) => (
                  <Steps.Step key={item.title} title={item.title} />
                ))}
              </Steps>
            </Flex>
            <Flex vertical className="legal-page-con" justify="center">
              <div
                style={{
                  height: "calc(100vh - 148px)",
                }}
              >
                {/* 文件入库 */}
                {current == 1 && (
                  <Card className="legal-page-library">
                    <Typography.Title className="file-title" level={5}>
                      入库方式
                    </Typography.Title>
                    <Flex style={{ marginBottom: "24px" }}>
                      <Input
                        prefix={<SearchOutlined />}
                        placeholder="搜索知识库"
                        disabled={true}
                        value={
                          knowledgeTypeChild == "2"
                            ? "手工选择"
                            : "智能入库（推荐）"
                        }
                      />
                    </Flex>
                    {/* 文件列表 */}
                    {uploadedFiles.length > 0 && (
                      <div className="file-list" style={{ maxHeight: "550px" }}>
                        <Typography.Text
                          className="section-title"
                          style={{ display: "block" }}
                        >
                          文档列表
                        </Typography.Text>
                        {uploadedFiles.map((file: any) => (
                          <div
                            key={file.fileId}
                            className="file-item file-item-ot"
                          >
                            <div className="file-item-title-name">
                              <Popover
                                placement="right"
                                className="file-item-ot-pop"
                                content={
                                  // <img src={previewFileUrl}></img>
                                  <div
                                    style={{ width: "600px", height: "800px" }}
                                  >
                                    <iframe
                                      src={String(previewFileUrl)}
                                      width="100%"
                                      height="100%"
                                      style={{ border: 0 }}
                                    />
                                  </div>
                                }
                                onOpenChange={(isOpen) =>
                                  openFileName(isOpen, file.fileId)
                                }
                              >
                                <IconFont
                                  type={formatIcon(file.title)}
                                  className="icon-font-margin"
                                />
                                <span
                                  className="file-item-name"
                                  style={{ display: "inline-block" }}
                                >
                                  {file.title}
                                </span>
                              </Popover>
                            </div>
                            <div className="file-item-icon">
                              <Popover
                                placement="left"
                                content={knowledgeContent}
                                onOpenChange={(isOpen) =>
                                  openKnowledgeContent(isOpen, file.libId)
                                }
                              >
                                <div className="file-item-ledge">
                                  <img src="" alt="" />
                                  <span>{file.libName}</span>
                                </div>
                              </Popover>
                              <EditOutlined
                                onClick={() => handleEdit(file.fileId)}
                                style={{
                                  marginRight: "10px",
                                  cursor: "pointer",
                                }}
                              />
                              <Popconfirm
                                title="删除确认"
                                description="确认删除该文档吗？"
                                onConfirm={() => handleDelete(file.fileId)}
                                // onCancel={cancel}
                                okText="确认"
                                cancelText="取消"
                              >
                                <DeleteOutlined
                                  style={{
                                    color: "#ff4d4f",
                                    cursor: "pointer",
                                  }}
                                />
                              </Popconfirm>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </Card>
                )}
                {/* 文件拆分 */}
                {current == 2 && (
                  <Flex vertical={false} className="file-split">
                    <div className="file-split-left">
                      <Typography.Title className="file-title" level={5}>
                        拆分方式
                      </Typography.Title>
                      <Flex style={{ marginBottom: "24px" }}>
                        <Input
                          prefix={<SearchOutlined />}
                          placeholder="拆分方式"
                          disabled={true}
                          value={
                            splitTypesChild == "3" ? "智能拆分（推荐)" : ""
                          }
                        />
                      </Flex>
                      {/* 文件列表 */}
                      {uploadedFiles.length > 0 && (
                        <div
                          className="file-list"
                          style={{ maxHeight: "550px" }}
                        >
                          <Typography.Text
                            className="section-title"
                            style={{ display: "block" }}
                          >
                            文档列表
                          </Typography.Text>
                          {syncSplitList.map((file: any) => (
                            <div
                              key={file.fileId}
                              className={
                                file.fileId == selectFileId
                                  ? "file-item file-item-ot file-item-active"
                                  : "file-item file-item-ot"
                              }
                              onClick={() => selectFile(file)}
                            >
                              <div className="file-item-con">
                                <IconFont
                                  type={formatIcon(file.title)}
                                  className="icon-font-margin"
                                />
                                <span className="file-item-name">
                                  {file.title}
                                </span>
                              </div>
                              <div className="file-item-icon">
                                <EditOutlined
                                  onClick={() => handleSplitEdit(file)}
                                  style={{
                                    marginRight: "10px",
                                    cursor: "pointer",
                                  }}
                                />
                                <Popconfirm
                                  title="删除确认"
                                  description="确认删除该文档吗？"
                                  onConfirm={() => handleDelete(file.fileId)}
                                  // onCancel={cancel}
                                  okText="确认"
                                  cancelText="取消"
                                >
                                  <DeleteOutlined
                                    style={{
                                      color: "#ff4d4f",
                                      cursor: "pointer",
                                    }}
                                  />
                                </Popconfirm>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="file-split-right">
                      <Typography.Title className="file-title" level={5}>
                        <IconFont
                          type={formatIcon(selectFileContent.title)}
                          className="icon-font-margin"
                        />
                        {selectFileContent.title}
                      </Typography.Title>
                      <Card className="file-split-right-card">
                        <Flex
                          justify="space-between"
                          style={{ marginBottom: "10px" }}
                        >
                          <Flex flex="1">
                            拆分片段：{selectFileContent.chunkSize}
                          </Flex>
                          <Flex flex="1">
                            拆分类型：
                            {formatSplitType(selectFileContent.splitType)}
                          </Flex>
                          <Flex flex="1"> </Flex>
                        </Flex>
                        <Flex justify="space-between">
                          <Flex flex="1">
                            分段标识符：{selectFileContent.chunkSegmentString}
                          </Flex>
                          <Flex flex="1">
                            分段最大长度：{selectFileContent.chunkMaxLength}
                          </Flex>
                          <Flex flex="1">
                            分段重叠度：{selectFileContent.chunkOverLapLength}
                          </Flex>
                        </Flex>
                      </Card>
                      <Typography.Title className="file-title" level={5}>
                        拆分预览
                      </Typography.Title>
                      <Flex className="split-show-item">
                        {selectFileContent.chunkList.length > 0 &&
                          selectFileContent.chunkList.map((x: any, index) => (
                            <Col
                              span={12}
                              key={index}
                              className="split-show-item-col"
                            >
                              <Card
                                title={selectFileContent.title}
                                className="split-card"
                                extra={
                                  <Button
                                    type="link"
                                    className="split-card-detail"
                                    icon={<ReadOutlined />}
                                    onClick={() =>
                                      setPreviewModal({
                                        open: true,
                                        title: selectFileContent.title,
                                        content: x.chunkText,
                                      })
                                    }
                                  >
                                    查看详情
                                  </Button>
                                }
                                style={{ width: "100%" }}
                              >
                                <div
                                  style={{
                                    display: "-webkit-box",
                                    WebkitLineClamp: 5,
                                    WebkitBoxOrient: "vertical",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "normal",
                                    minHeight: "110px",
                                    lineHeight: token.lineHeight,
                                  }}
                                >
                                  {x.chunkText}
                                </div>
                              </Card>
                            </Col>
                          ))}
                      </Flex>
                    </div>
                  </Flex>
                )}
                {/* 处理并完成 */}
                {current == 3 && (
                  <Card className="legal-page-library legal-page-completed">
                    <Flex vertical justify="center" align="center">
                      <Typography.Title
                        level={2}
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <IconFont
                          type="finish"
                          className="icon-font-margin icon-font-finish"
                        />
                        <span>处理完成</span>
                      </Typography.Title>
                      <Flex vertical className="completed-upload">
                        <Typography.Title level={3}>
                          <i>文档已上传</i>
                        </Typography.Title>
                        <Typography.Text>
                          文档已上传至知识库，你可以在知识库的文档列表中找到它
                        </Typography.Text>
                        {/* 文件列表 */}
                        {uploadedFiles.length > 0 && (
                          <div className="file-list file-list-completed">
                            {uploadedFiles.map((file: any) => (
                              <div key={file.fileId} className="file-item">
                                <IconFont
                                  type={formatIcon(file.title)}
                                  className="icon-font-margin"
                                />
                                <span className="file-item-title">
                                  {file.title}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </Flex>
                    </Flex>
                  </Card>
                )}
              </div>
              {
                <Flex
                  justify="center"
                  align="center"
                  gap={token.marginMD}
                  className="legal-review-footer"
                >
                  <Button
                    style={{ minWidth: "100px", marginRight: 8 }}
                    onClick={() => {
                      setCurrent(0);
                      setUploadedFiles([]);
                    }}
                  >
                    返回首页
                  </Button>

                  {current > 0 && current !== 3 && (
                    <Button
                      style={{ minWidth: "100px", marginRight: 8 }}
                      onClick={onPerv}
                    >
                      上一步
                    </Button>
                  )}
                  {current < pageInfo.steps.length - 1 && (
                    <Button
                      type="primary"
                      onClick={getSubmitInfo}
                      style={{ minWidth: "100px", marginRight: 8 }}
                    >
                      {currentStep?.submitName || "下一步"}
                    </Button>
                  )}
                </Flex>
              }
            </Flex>
          </Flex>
        )}
      </Flex>
      <Modal
        width={695}
        title="选择入库方式"
        closable={{ "aria-label": "Custom Close Button" }}
        okText="下一步"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Flex
          className="knowledge-modal"
          justify="center"
          align="center"
          gap="28px"
        >
          <Card
            className={knowledgeType == "1" ? "knowledge-item" : ""}
            onClick={() => selectKnowledgeType("1")}
          >
            <IconFont type="AIChatFilled" className="icon-font-margin" />
            <span>智能入库（推荐）</span>
          </Card>
          <Card
            className={knowledgeType == "2" ? "knowledge-item" : ""}
            onClick={() => selectKnowledgeType("2")}
          >
            <IconFont
              type="knowledgeBaseOutlined"
              className="icon-font-margin"
            />
            <span>选择知识库</span>
          </Card>
        </Flex>
        {knowledgeType && knowledgeType == "2" && (
          <Flex className="knowledge-modal">
            <Card style={{ width: "100%" }}>
              <Flex style={{ marginBottom: "24px" }}>
                <Input
                  value={knowledgeTypeValue}
                  prefix={<SearchOutlined />}
                  placeholder="搜索知识库"
                  onInput={(e) => searchKnowledge(e)}
                />
              </Flex>
              <Flex
                vertical
                style={{ maxHeight: "258px", overflowY: "scroll" }}
              >
                {knowledgeList.map((item: any) => (
                  <div
                    key={item.id}
                    className={
                      item.id == currentId
                        ? "knowledge-list knowledge-list-modal knowledge-list-active"
                        : "knowledge-list knowledge-list-modal"
                    }
                    style={{ padding: "4px 10px", borderRadius: "4px" }}
                    onClick={() => selectKnowledge(item)}
                  >
                    <IconFont
                      type="knowledgeBaseOutlined"
                      className="icon-font-margin"
                    />
                    <span className="knowledge-list-modal-title">
                      {item.libName}
                    </span>
                  </div>
                ))}
              </Flex>
            </Card>
          </Flex>
        )}
      </Modal>
      <Modal
        width={695}
        title="选择知识库"
        open={isKnowledgeModalOpen}
        onOk={handleKnowledgeOk}
        onCancel={handleKnowledgeCancel}
      >
        {
          <Flex className="knowledge-modal">
            <Card style={{ width: "100%" }}>
              <Flex style={{ marginBottom: "24px" }}>
                <Input
                  value={libraryIpt}
                  prefix={<SearchOutlined />}
                  placeholder="搜索知识库"
                  onInput={(e) => searchLibraryIpt(e)}
                />
              </Flex>
              <Flex
                vertical
                style={{ maxHeight: "258px", overflowY: "scroll" }}
              >
                {knowledgeList.map((item: any) => (
                  <div
                    key={item.id}
                    className={
                      item.id == currentId
                        ? "knowledge-list knowledge-list-modal knowledge-list-active"
                        : "knowledge-list knowledge-list-modal"
                    }
                    style={{ padding: "4px 10px", borderRadius: "4px" }}
                    onClick={() => selectKnowledge(item)}
                  >
                    <IconFont
                      type="knowledgeBaseOutlined"
                      className="icon-font-margin"
                    />
                    <span className="knowledge-list-modal-title">
                      {item.libName}
                    </span>
                  </div>
                ))}
              </Flex>
            </Card>
          </Flex>
        }
      </Modal>
      <Modal
        width={695}
        title="选择拆分方式"
        closable={{ "aria-label": "Custom Close Button" }}
        okText="下一步"
        open={isSplitModalOpen}
        onOk={handleSplitOk}
        onCancel={handleSplitCancel}
      >
        <Flex
          className="knowledge-modal"
          justify="center"
          align="center"
          gap="28px"
        >
          <Card
            className={splitTypes == "3" ? "knowledge-item" : ""}
            onClick={() => selectSplit("3")}
          >
            <IconFont type="AIChatFilled" className="icon-font-margin" />
            <span>智能拆分（推荐）</span>
          </Card>
          <Card
            className={splitTypes == "4" ? "knowledge-item" : ""}
            onClick={() => selectSplit("4")}
          >
            <StopOutlined style={{ marginRight: "6px" }} />
            <span>不拆分</span>
          </Card>
        </Flex>
      </Modal>
      <Modal
        open={previewModal.open}
        title={previewModal.title}
        footer={null}
        onCancel={() =>
          setPreviewModal({ open: false, title: "", content: "" })
        }
        className="preview-modal"
        style={{
          top: "15vh",
          width: "60vw",
          maxHeight: "70vh",
          minHeight: "500px",
          overflowY: "auto",
        }}
        width="70vw"
      >
        <div className="name" style={{ whiteSpace: "pre-wrap" }}>
          {(previewModal.content || "").replace(/\n{3,}/g, "\n\n")}
        </div>
      </Modal>
      <Modal
        width={695}
        title="拆分设置"
        closable={{ "aria-label": "Custom Close Button" }}
        okText="确认"
        open={isSplitTypeOpen}
        onOk={handleSplitTypeOk}
        onCancel={handleSplitTypeCancel}
      >
        <Flex
          justify="space-between"
          gap="15px"
          style={{ marginBottom: "10px" }}
        >
          <Flex vertical flex="1">
            <div className="split-setting-title">拆分类型</div>
            <div>
              <Input
                placeholder="拆分类型"
                disabled={true}
                value={fileSplitType}
              />
            </div>
          </Flex>
          <Flex vertical flex="1">
            <div></div>
          </Flex>
          <Flex vertical flex="1">
            <div></div>
          </Flex>
        </Flex>
        <Flex
          justify="space-between"
          gap="15px"
          style={{ marginBottom: "10px" }}
        >
          <Flex flex="1" vertical>
            <div className="split-setting-title">分段标识符</div>
            <div>
              <Input
                placeholder="分段标识符"
                value={segmentationMarker}
                onChange={changeChunkSegmentString}
              />
            </div>
          </Flex>
          <Flex flex="1" vertical>
            <div className="split-setting-title">分段最大长度</div>
            <div>
              <InputNumber
                style={{ width: "100%" }}
                placeholder="分段最大长度"
                value={chunkMaxLength}
                onChange={(value) => changeChunkMaxLength(value)}
              />
            </div>
          </Flex>
          {fileSplitType == "普通" && (
            <Flex flex="1" vertical>
              <div className="split-setting-title">分段重叠长度</div>
              <div>
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="分段重叠长度"
                  value={chunkOverLapLength}
                  onChange={(value) => changeChunkOverLapLength(value)}
                />
              </div>
            </Flex>
          )}
        </Flex>
      </Modal>
    </>
  );
};

export default DraftPage;
