// DraftPage.tsx
import React, {
  useState,
  useMemo,
  Suspense,
  useRef,
  createRef,
  RefObject,
  useEffect,
} from "react";
import {
  Button,
  Flex,
  Form,
  message,
  Upload,
  Select,
  Spin,
  Typography,
  theme,
  Steps,
} from "antd";
import HeaderCom from "@/component/Header";
import uploadIcon from "@/assets/images/public/upload.png";
import {
  fileToBase64,
  extractJsonString,
  preprocessJsonForDisplay,
} from "@/utils/common";
import useSSEChat from "@/hooks/useSSEChat";
import { convertFileToPDF, uploadChatFile, scenarioDetail } from "@/api/public";
import Collaboration from "@/component/Collaboration";
import IconFont from "@/component/IconFont";
import { saveToKnowledgeBase } from "@/utils/saveKnowledge";
import { getToken, getUserInfo } from "@/utils/auth";
import ReviewResult from "@/component/ReviewResult";
import { cacheGet } from "@/utils/cacheUtil";
import "./index.less";
import { DeleteOutlined, RedoOutlined, TeamOutlined } from "@ant-design/icons";
import { useLocation } from "react-router-dom";
import { Content } from "antd/es/layout/layout";
const { useToken } = theme;
const VITE_SCENE_INPUT: string = import.meta.env["VITE_SCENE_INPUT"] || ""; // 场景输入官
const VITE_SCENE_OUTPUT: string = import.meta.env["VITE_SCENE_OUTPUT"] || ""; // 场景输出官
const VITE_UNIT_INPUT: string = import.meta.env["VITE_UNIT_INPUT"] || ""; // 单元场景输入评审
const VITE_UNIT_OUTPUT: string = import.meta.env["VITE_UNIT_OUTPUT"] || ""; // 单元场景输出评审
const VITE_SCENE_PLANNING: string =
  import.meta.env["VITE_SCENE_PLANNING"] || ""; // 场景规划师

// 自动匹配业务组件
const modules = import.meta.glob("/src/businessComponents/**/index.tsx");

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat();
  const { token } = useToken();
  const [pageInfo, setPageInfo] = useState<any>({
    pageName: "智能合同审查助手",
    pageDesc: "法审、敏感词、批注",
    pageInputDesc: "用户上传需要进行法审的合同内容",
    pageOutputDesc: "含有批注内容的word文档",
    pageOutPutData: {}, // 存储 startData
    steps: [
      {
        title: "文档拆分",
        content: "<SplitviewModule />",
        submitName: "",
        isExport: false,
        agentId: "",
        unitOutPutData: {}, // 存储步骤0的输出数据
      },
      {
        title: "实体提取",
        content: "<EntityExtractionModule />",
        submitName: "",
        isExport: false,
        agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
        inputSource: "文档切分，场景规划师",
        unitOutPutData: {}, // 存储步骤1的输出数据
      },
      {
        title: "规则与目标确认",
        content: "<TargetingModule />",
        submitName: "",
        isExport: false,
        agentId: "a5f3cb88-d63b-417c-ab81-90c1307e2c31",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
        unitOutPutData: {}, // 存储步骤2的输出数据
      },
      {
        title: "合同法审",
        content: "<ContractLawReview />",
        submitName: "",
        isExport: true,
        agentId: "06d5b6f0-fdd1-4f7c-91d7-4f73c5f89ad1",
        inputDesc: "用户上传合同内容和规则条例",
        outputDesc: "输出合同校验结果",
        inputSource: "规则匹配",
        unitOutPutData: {}, // 存储步骤3的输出数据
      },
    ],
  });

  const splitTypeOptions = [{ label: "按章节切分", value: "按章节切分" }];
  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const messageId = searchParams.get("messageId") || "";
  const [current, setCurrent] = useState(-1);
  // 2️⃣ 用 ref 始终保存最新的 current 值（避免闭包问题）
  const currentRef = useRef(current);
  const [sceneReviewStream, setSceneReviewStream] = useState<any>(""); // 场景规划流式输出
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]); // 上传的文件信息
  const [originalFile, setOriginalFile] = useState<any>(null); // 原始上传的文件
  const [splitType, setSplitType] = useState(splitTypeOptions[0].value); // 切分类型
  const [sceneReviewShow, setSceneReviewShow] = useState(false); // 是否要展示场景评审
  const [sceneReviewContent, setSceneReviewContent] = useState<any>(null); // 单元场景输入输出评审的内容
  const [scenePageReviewContent, setScenePageReviewContent] =
    useState<any>(null); // 场景输入输出评审的内容
  const [reviewResult, setReviewResult] = useState<any>(null); // 输入输出评审的结果
  const [scenarioSteps, setScenarioSteps] = useState<any[]>([]); // 场景评审步骤
  const currentStep = pageInfo.steps[current]; // 当前步骤信息
  const [isReview, setIsReview] = useState(false); // 是否是评审
  const [collOpen, setCollOpen] = useState(false); // 协同弹框
  const [isShow, setIsShow] = useState(false); // 是否要调用接口回显
  const [isModalUnit, setIsModalUnit] = useState(false); // 弹框显示
  const [unitTitle, setUnitTitle] = useState("场景输入评审");

  const isOutputReview = useRef(false); // 看是输入评审还是输出评审

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({});

  //取 pageInfo 自己的基本信息 + steps 里某一步
  const getStepData = (pageInfo: any, stepIndex: number) => {
    const { steps, ...pageBase } = pageInfo; // 把 steps 拆出来，剩下的是 pageInfo 自身信息
    const step = steps[stepIndex]; // 拿到某一步

    return {
      ...pageBase,
      step, // 保留单步
    };
  };

  // 步骤条的点击
  const stepChange = async (value: number) => {
    if (pageInfo.steps[value]?.unitOutPutData?.outputReview) {
      const ref = refs.current[currentRef.current];
      const childData = await ref?.current?.triggerSplit?.();
      setPageInfo((prev) => {
        const newPageInfo = { ...prev };
        newPageInfo.steps[currentRef.current].unitOutPutData = {
          ...newPageInfo.steps[currentRef.current].unitOutPutData,
          ...childData,
        };
        return newPageInfo;
      });
      setCurrent(value);
    } else {
      message.warning("请完成当前步骤");
    }
  };

  useEffect(() => {
    // 回显示赋值
    if (pageInfo?.pageOutPutData?.inputReview) {
      setUploadedFiles(pageInfo?.pageOutPutData?.uploadedFiles);
      setOriginalFile(pageInfo?.pageOutPutData?.originalFile);
      setSplitType(pageInfo?.pageOutPutData?.splitType);
    }
    if (messageId && !isShow) {
      scenarioDetail({ id: messageId }).then((res: any) => {
        if (res.code == 200) {
          setIsShow(true);
          const data = JSON.parse(res.data?.pageInfo);
          setPageInfo(data);
          setCurrent(res.data?.currentStep);
          setUploadedFiles(data?.pageOutPutData?.uploadedFiles);
          setOriginalFile(data?.pageOutPutData?.originalFile);
          setSplitType(data?.pageOutPutData?.splitType);
        }
      });
    }
  }, []);
  useEffect(() => {
    currentRef.current = current;
  }, [current]);

  // 懒加载当前组件
  const DynamicComponent = useMemo(() => {
    if (current < 0) return;
    const match = currentStep?.content?.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];

    if (!componentName) return null;

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
    const loader = modules[modulePath];

    if (!loader) {
      return null;
    }

    // 创建 ref（如果不存在）
    if (!refs.current[current]) {
      refs.current[current] = createRef();
    }

    const LazyComponent = React.lazy(loader as any);

    return (props: any) => (
      <LazyComponent ref={refs.current[current]} {...props} />
    );
  }, [current]);

  // 提交操作
  const getSubmitInfo = async () => {
    const ref = refs.current[currentRef.current];
    let childData: any = null;
    if (currentRef.current == 0) {
      childData = await ref?.current?.triggerSplit?.();
    } else if (currentRef.current == 1) {
      childData = await ref?.current?.triggerSplit?.();
      if (childData.isEntity == true) {
        await ref?.current?.showModal?.();
        return;
      }
    } else if (currentRef.current == 2) {
      childData = await ref?.current?.triggerSplit?.();
      if (!childData?.selectedRules?.length) {
        message.open({
          type: "error",
          content: "请选择规则",
        });
        return;
      }
      if (pageInfo.steps[2]?.unitOutPutData?.selectedRules?.length > 0) {
        const isEqualIgnoreOrder =
          pageInfo.steps[2]?.unitOutPutData?.selectedRules.length ===
            childData?.selectedRules.length &&
          pageInfo.steps[2]?.unitOutPutData?.selectedRules.every((val) =>
            childData?.selectedRules.includes(val)
          );
        if (!isEqualIgnoreOrder) {
          setPageInfo((prev) => {
            const newPageInfo = { ...prev };
            // 直接清除最后一步（已知 lastIndex == 3）
            if (newPageInfo.steps[3]) {
              newPageInfo.steps[3].unitOutPutData = {
                inputReview: false,
                outputReview: false,
              };
            }
            return newPageInfo;
          });
        }
      }
    } else {
      childData = await ref?.current?.triggerSplit?.();
    }

    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });
    if (currentRef.current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
      // const obj = getStepData(pageInfo, current + 1);
      // if (!stepData[current + 1]?.inputReview) {
      //   getUnitReviewInfo(
      //     false,
      //     VITE_UNIT_INPUT,
      //     "1",
      //     obj,
      //   );
      // }
    }
  };

  // 点击上一步存储当前页面的数据
  const onPerv = async () => {
    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });
    if (currentRef.current > -1) {
      setCurrent(current - 1);
    }
  };
  // 进入下一步 由子组件跳转进入
  const handleIncrement = async () => {
    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();
    setPageInfo((prev) => {
      const newPageInfo = { ...prev };
      newPageInfo.steps[currentRef.current].unitOutPutData = {
        ...newPageInfo.steps[currentRef.current].unitOutPutData,
        ...childData,
      };
      return newPageInfo;
    });
    if (currentRef.current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
    }
  };

  // 上传文件
  const beforeUpload = async (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf("."));
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    setGlobalLoading?.(true);
    if (["docx", "doc"].includes(originalFileExt)) {
      convertFileToPDF(file).then(async (response) => {
        if (response["status"] && response["status"] !== 200) {
          setGlobalLoading?.(false);
          message.open({
            key: "uploading",
            type: "error",
            content: "文件处理异常，请稍后重试",
            duration: 1,
          });
        } else if ("blob" in response) {
          const userInfo = await getUserInfo();
          const blob = await response.blob();
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: "application/pdf",
          });
          const fileData = {
            fileName: file.name,
            fileStr: await fileToBase64(file),
            path: "/files/upload",
            agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
            user: userInfo?.id,
            libName: file.name,
            libDesc: "",
            flag: "file",
          };
          uploadChatFile(fileData).then(async (response: any) => {
            setGlobalLoading?.(false);
            if (response.code == 200) {
              setUploadedFiles([
                { url: URL.createObjectURL(pdfFile), ...response.data },
              ]);
              setOriginalFile(file);
              // 因为文件重新上传了，所以需要清空之前所有的评审跟数据
              setScenarioSteps([]);
              setPageInfo((prev) => {
                const newPageInfo = { ...prev };

                // 整个 pageOutPutData 置空对象
                newPageInfo.pageOutPutData = {};

                // steps 中每个 step 的 unitOutPutData 置空对象
                newPageInfo.steps = newPageInfo.steps.map((step: any) => ({
                  ...step,
                  unitOutPutData: {},
                }));

                return newPageInfo;
              });
              message.open({
                key: "uploading",
                type: "success",
                content: "文件上传成功",
                duration: 1,
              });
            } else {
              message.open({
                key: "uploading",
                type: "error",
                content: "文件上传失败",
                duration: 1,
              });
            }
          });
        }
      });
    }
  };
  // 首页点击下一步
  const nextContent = async () => {
    console.log(uploadedFiles, 444);
    if (uploadedFiles.length === 0) {
      message.error("请先上传文件");
      return;
    }
    // setCurrent(0);
    setPageInfo((prev) => ({
      ...prev,
      pageOutPutData: {
        ...prev.pageOutPutData,
        originalFile: originalFile, // 原始文件
        uploadedFiles: uploadedFiles, // pdf的文件
        splitType: splitType, // 拆分方式
      },
    }));
    if (pageInfo?.pageOutPutData?.inputReview) {
      // 首页已经规划
      // 说明是已经规划过
      setCurrent(0);
      if (!pageInfo?.steps[0]?.unitOutPutData?.inputReview) {
        // 说明是已经输入还没有评审过
        const obj = getStepData(pageInfo, 0);
        getUnitReviewInfo(
          false,
          VITE_UNIT_INPUT,
          "1",
          obj,
          pageInfo?.pageOutPutData?.uploadedFiles
        );
      }
    } else {
      setSceneReviewShow(true);
      getReviewInfo(
        VITE_SCENE_INPUT,
        `选择拆分方式:按章节拆分`,
        pageInfo,
        uploadedFiles
      );
    }
  };
  // 开始场景规划轮训处理
  const sceneReviewStartFn = async () => {
    setGlobalLoading(true);
    const result: any[] = [];
    // 循环调用每个步骤，一个完成后再调用下一个
    for (const item of pageInfo.steps) {
      const stepResult: any = await getPlanning({
        pageName: pageInfo.pageName,
        pageDesc: pageInfo.pageDesc,
        pageInputDesc: pageInfo.pageInputDesc,
        pageOutputDesc: pageInfo.pageOutputDesc,
        steps: [item],
      });
      const jsonStr = extractJsonString(stepResult);
      // 预处理 JSON 字符串，避免 ReactMarkdown 渲染错误
      const processedJsonStr = preprocessJsonForDisplay(jsonStr);
      setSceneReviewStream((prev: string) => prev + processedJsonStr + "\n\n");
      try {
        if (typeof jsonStr == "string") {
          // 是 JSON 字符串，需要解析
          result.push(JSON.parse(jsonStr));
        } else if (typeof jsonStr == "object") {
          // 是 JSON 对象，可以直接使用
          result.push(jsonStr);
        } else {
          console.error("解析 JSON 失败: ", jsonStr);
        }
      } catch (e) {
        console.error("解析 JSON 失败: ", e, jsonStr);
      }
    }
    // 1) 融合：将规划结果与原始 step 元数据合并（按顺序一一对应）
    const merged = pageInfo.steps.map((step: any, idx: any) => ({
      ...step,
      ...(result[idx] || {}),
    }));
    setScenarioSteps(merged);
    setGlobalLoading(false);
  };
  // 规划
  const getPlanning = async (data: any) => {
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    const tenantId = cacheGet("tenantId");
    return new Promise((resolve) => {
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: VITE_SCENE_PLANNING || "",
          agentId: VITE_SCENE_PLANNING || "",
          path: "/chat-messages",
          query: "1",
          difyJson: {
            inputs: {
              Token: tokenInfo || "",
              tenantid: tenantId || "",
              pageInfo: JSON.stringify(data),
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: "1",
          },
        },
        query: {},
        message: "1",
        onFinished: (result: any) => {
          resolve(result); // 将结果传出去
        },
      });
    });
  };
  // 子组件调用场景输出评审
  const childReviewInfo = (type: string, data: any, fileList?: any) => {
    if (type == "输入") {
      getReviewInfo(VITE_SCENE_INPUT, data, pageInfo, fileList);
    } else {
      getReviewInfo(VITE_SCENE_OUTPUT, data, pageInfo);
    }
  };
  // 场景输入输出评审信息
  const getReviewInfo = async (
    agentId: string,
    inputQuery: string,
    pageInfo: any,
    uploadedFileData?: any
  ) => {
    const fileData: any[] = [];
    setGlobalLoading(true);
    setScenePageReviewContent(null);
    uploadedFileData?.forEach((item: any) => {
      fileData.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    try {
      if (agentId) {
        const tokenInfo = await getToken();
        const userInfo = await getUserInfo();
        const tenantId = cacheGet("tenantId");
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },

          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId,
            agentId: agentId,
            path: "/chat-messages",
            query: inputQuery || "",
            difyJson: {
              inputs: {
                Token: tokenInfo || "",
                tenantid: tenantId || "",
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: inputQuery || "",
              files: fileData,
            },
          },
          query: {},
          message: inputQuery || "",
          onMessage: (res: any) => {
            console.log(res, "输入/输出评审内容");
          },
          onFinished: (res) => {
            if (res) {
              const jsonStr = extractJsonString(res);
              try {
                const parsed = JSON.parse(jsonStr);
                console.log(parsed, 12121);
                if (parsed["评审结果"] == "正常") {
                  setReviewResult(parsed["评审结果"] == "正常");
                  if (agentId == VITE_SCENE_INPUT) {
                    setScenePageReviewContent(parsed);
                    // 开始场景规划
                    sceneReviewStartFn();
                  } else {
                    setGlobalLoading(false);
                  }
                } else {
                  setReviewResult(parsed["评审结果"] == "异常");
                  setScenePageReviewContent(parsed);
                  setGlobalLoading(false);
                }
              } catch (e) {
                console.error("输入/输出评审结果解析失败:", e, jsonStr);
              }
            }
          },
        });
      }
    } catch (error) {
      setGlobalLoading(false);
    }
  };

  // 忽略错误，继续下一步
  const continueScenario = () => {
    // 这个是场景评审的
    sceneReviewStartFn();
  };

  // 规划页面修改 确认无误
  const confirmScenario = (steps: any) => {
    setPageInfo((prev) => ({
      ...prev,
      steps: steps,
      pageOutPutData: {
        ...prev.pageOutPutData,
        inputReview: true,
      },
    }));
    setSceneReviewShow(false);
    if (currentRef.current < 0) {
      setCurrent(0);
      if (!pageInfo?.pageOutPutData?.inputReview) {
        // 说明是已经输入还没有评审过
        const obj = getStepData(pageInfo, 0);
        getUnitReviewInfo(
          false,
          VITE_UNIT_INPUT,
          "1",
          obj,
          pageInfo?.pageOutPutData?.uploadedFiles
        );
      }
    }
  };

  // 单元场景的评审～～～～～～～～～～～～～～～～～～～～～～～～～～～～～～～

  // 输入评审完毕后，调用各个单元的提交.  重新生成
  const getSubmit = (type?: string) => {
    const ref = refs.current[currentRef.current];
    if (type == "1") {
      // 如果是1说明是重新生成，需要清空后面的值
      setPageInfo((prev) => {
        const newPageInfo = { ...prev };
        // 清空当前步骤及后续步骤的数据
        for (let i = currentRef.current; i < pageInfo.steps.length; i++) {
          if (newPageInfo.steps[i]) {
            // 保留基础数据，清空评审状态和生成的内容
            newPageInfo.steps[i].unitOutPutData = {
              inputReview: false,
              outputReview: false,
            };
          }
        }
        return newPageInfo;
      });
    }
    if (currentRef.current == 0) {
      ref?.current?.splitDataFiles?.();
    } else if (currentRef.current == 1) {
      ref?.current?.getQualityData?.();
    } else if (currentRef.current == 2) {
      ref?.current?.getRuleData?.();
    } else if (currentRef.current == 3) {
      ref?.current?.getContractData?.();
    }
  };

  // 单元场景评审失败， 继续下一步点击
  const unitContinueScenario = (type?: string, data?: any) => {
    console.log(type, data, "2312type，其次用31");
    // 优先用传入的 type，其次用 isOutputReview.current
    let isOutput: boolean;
    if (type) {
      if (type === "输出") {
        isOutput = true;
      } else if (type === "输入") {
        isOutput = false;
      } else {
        // 如果 type 传了奇怪的值，就回退用原来的逻辑
        isOutput = isOutputReview.current;
      }
    } else {
      isOutput = isOutputReview.current;
    }

    if (isOutput) {
      // 输出评审
      setPageInfo((prev) => {
        const newPageInfo = { ...prev };
        newPageInfo.steps[currentRef.current].unitOutPutData = {
          ...newPageInfo.steps[currentRef.current]?.unitOutPutData,
          outputReview: true,
          outputData: data,
        };
        return newPageInfo;
      });
    } else {
      // 输入评审
      setPageInfo((prev) => {
        const newPageInfo = { ...prev };
        newPageInfo.steps[currentRef.current].unitOutPutData = {
          ...newPageInfo.steps[currentRef.current]?.unitOutPutData,
          inputReview: true,
          inputData: data,
        };
        return newPageInfo;
      });
      getSubmit();
    }

    setIsReview(false);
  };

  // 子组件调用输出评审（子组件传过来的）
  // stepCurrent 第几步
  // stepCurrentData 第几步的数据
  const handleFromChild = (type: string, stepCurrentData: any) => {
    const obj = getStepData(pageInfo, currentRef.current);
    let isRtye = false;
    if (type == "输出") {
      isRtye = true;
    }
    getUnitReviewInfo(isRtye, VITE_UNIT_OUTPUT, stepCurrentData, obj);
  };
  // 输入输出评审单元的
  const getUnitReviewInfo = async (
    isOutput: boolean,
    agentId: string,
    inputQuery?: string,
    pageInfo?: any,
    uploadedFileData?: any
  ) => {
    const fileData: any[] = [];
    setReviewResult(null);
    setIsReview(true);
    setGlobalLoading(true);
    setSceneReviewContent(null);
    if (uploadedFileData && uploadedFileData.length > 0) {
      uploadedFileData?.forEach((item: any) => {
        fileData.push({
          type: item.fileType || "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });
    }
    isOutputReview.current = isOutput; // 看当前是输入还是输出评审
    try {
      if (agentId) {
        const tokenInfo = await getToken();
        const userInfo = await getUserInfo();
        const tenantId = cacheGet("tenantId");
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },

          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId,
            agentId: agentId,
            path: "/chat-messages",
            query: inputQuery || "",
            difyJson: {
              inputs: {
                Token: tokenInfo || "",
                tenantid: tenantId || "",
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: inputQuery || "",
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || "",
          onMessage: (res: any) => {
            console.log(res, "输入/输出评审内容");
          },
          onFinished: (res) => {
            if (res) {
              const jsonStr = extractJsonString(res);
              try {
                const parsed = JSON.parse(jsonStr);
                if (parsed["评审结果"] == "正常") {
                  setReviewResult(parsed["评审结果"] == "正常");
                } else {
                  setReviewResult(parsed["评审结果"] == "异常");
                  // setSceneReviewContent(parsed);
                }
                setGlobalLoading(false);
                const type = isOutput ? "输出" : "输入";
                unitContinueScenario(type, parsed);
                setIsReview(false);
              } catch (e) {
                console.error("输入/输出评审结果解析失败:", e, jsonStr);
              }
            }
          },
        });
      }
    } catch (error) {
      setGlobalLoading(false);
    }
  };
  // 导出
  const exportBtn = () => {
    const ref = refs.current[currentRef.current];
    if (currentRef.current == 3) {
      ref?.current?.exportData?.();
    }
  };
  // 文件删除
  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };

  // 存入知识库
  const addKnowledgeBase = async () => {
    const ref = refs.current[currentRef.current];
    let arr: any = [];
    const data = await ref?.current?.triggerSplit?.();
    if (currentRef.current == 0) {
      arr = data?.chunksData;
    } else if (currentRef.current == 1) {
      arr = data?.parsedData;
    } else if (currentRef.current == 2) {
      arr = data?.rulesData;
    } else if (currentRef.current == 3) {
      arr = data?.allData;
    }
    setGlobalLoading(true);
    const done = await saveToKnowledgeBase(
      pageInfo.pageName,
      pageInfo.pageDesc,
      pageInfo?.steps[currentRef.current]?.title,
      JSON.stringify(arr)
    );
    if (done) {
      console.log("已经请求完毕且成功");
      setGlobalLoading(false);
    } else {
      console.log("请求失败或未完成");
      setGlobalLoading(false);
    }
  };

  // 协同
  const handleOk = async (res: any) => {
    if (res.code == 200) {
      setCollOpen(false);
    }
  };

  // 点击查看评审结果详情
  const seeResult = (type: number) => {
    let data: any = null;
    if (type == 1) {
      setUnitTitle("场景输入评审");
      data = pageInfo.steps[currentRef.current]?.unitOutPutData?.inputData;
    } else {
      setUnitTitle("场景输出评审");
      data = pageInfo.steps[currentRef.current]?.unitOutPutData?.outputData;
    }
    setSceneReviewContent(data);
    setIsModalUnit(true);
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex vertical className="legal-review-page">
        {/* 首页 */}
        {current < 0 ? (
          <Flex vertical className="legal-review-home">
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />
            {sceneReviewShow ? (
              <ReviewResult
                type="scene"
                steps={scenarioSteps.length ? scenarioSteps : []}
                showScenario={scenarioSteps.length > 0 || sceneReviewStream}
                reviewContent={scenePageReviewContent}
                reviewResult={reviewResult}
                onBackIndex={() => {
                  setSceneReviewShow(false);
                }}
                sceneStream={sceneReviewStream}
                onContinue={() => continueScenario()}
                onConfirmScenario={(steps) => confirmScenario(steps)}
              />
            ) : (
              <Flex
                vertical
                gap="middle"
                style={{ flex: 1 }}
                className="splitting-form"
              >
                <Form layout="vertical">
                  <Form.Item>
                    <Flex className="upload">
                      <Flex vertical gap="4" style={{ flex: 1 }}>
                        <Typography.Title className="file-title" level={5}>
                          合同上传
                        </Typography.Title>
                        <Upload.Dragger
                          showUploadList={false}
                          multiple={false}
                          beforeUpload={beforeUpload}
                          accept=".docx"
                          fileList={uploadedFiles}
                        >
                          <img
                            src={uploadIcon}
                            style={{ width: 45, margin: "0px auto" }}
                          />
                          <p className="ant-upload-hint">
                            <span>点击或将文件拖到此处上传</span>
                            <span>支持word格式文档</span>
                          </p>
                        </Upload.Dragger>
                        {uploadedFiles?.length > 0 && (
                          <div
                            className="file-list-contract"
                            style={{ margin: "12px 0" }}
                          >
                            {uploadedFiles.map((file) => (
                              <div
                                key={file.id}
                                className="file-item"
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 8,
                                }}
                              >
                                <span>{file.name}</span>
                                <DeleteOutlined
                                  onClick={() => handleDelete(file.id)}
                                  style={{ cursor: "pointer" }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </Flex>
                    </Flex>
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span style={{ color: "#000", fontWeight: "bold" }}>
                          选择拆分方式
                        </span>
                      </>
                    }
                  >
                    <Select
                      value={splitType}
                      options={splitTypeOptions}
                      onChange={setSplitType}
                      placeholder="请选择拆分方式"
                    />
                  </Form.Item>
                </Form>
                <Button
                  size="large"
                  type="primary"
                  className="upload-btn"
                  onClick={nextContent}
                >
                  下一步
                </Button>
              </Flex>
            )}
          </Flex>
        ) : (
          // 步骤页
          <Flex vertical className="legal-review-steps">
            <Flex className="info-con-steps" justify="center" align="center">
              <div className="content-title">
                <p>智能合同审查助手</p>
                <p>法审、敏感词、批注</p>
              </div>
              <Steps
                current={current}
                className="steps-con"
                onChange={stepChange}
              >
                {pageInfo.steps.map((item) => (
                  <Steps.Step key={item.title} title={item.title} />
                ))}
              </Steps>
            </Flex>
            <Flex vertical className="legal-page-con" justify="center">
              <div
                style={{
                  height: "calc(100vh - 148px)",
                }}
              >
                <Suspense
                  fallback={
                    <Content
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: "calc(100vh - 150px)", // 减去 header 高度
                      }}
                    >
                      {/* <Spin size="large" tip="加载中..." /> */}
                    </Content>
                  }
                >
                  {DynamicComponent ? (
                    <>
                      <div
                        style={{
                          display:
                            isReview && sceneReviewContent ? "block" : "none",
                        }}
                      >
                        <ReviewResult
                          type="unit"
                          steps={
                            scenarioSteps.length
                              ? scenarioSteps
                              : pageInfo.steps
                          }
                          title={unitTitle}
                          isModalOpen={isModalUnit}
                          unitOk={() => {
                            setIsModalUnit(false);
                          }}
                          unitOnCancel={() => {
                            setIsModalUnit(false);
                          }}
                          showScenario={scenarioSteps.length > 0}
                          reviewContent={sceneReviewContent}
                          reviewResult={reviewResult}
                          onBackIndex={() => {
                            setSceneReviewShow(false);
                          }}
                          onContinue={() => unitContinueScenario()}
                        />
                      </div>

                      <div
                        style={{
                          display:
                            !isReview || !sceneReviewContent ? "block" : "none",
                        }}
                      >
                        <DynamicComponent
                          setGlobalLoading={setGlobalLoading}
                          agentId={currentStep.agentId}
                          unitOutPutData={
                            pageInfo?.steps[current]?.unitOutPutData
                          } // 父组件当前的值用于回显跟判断 有无输入输出
                          onIncrement={handleIncrement} // 子组件调用父组件的方法进入下一步
                          onCallParent={handleFromChild} // 子组件调用父组件的方法 进行单元场景输出处理
                          onGetReviewInfo={childReviewInfo} // 获取场景评审信息
                          unitInputData={{
                            // 公共数据
                            fileList: pageInfo?.pageOutPutData?.uploadedFiles,
                            chunks:
                              pageInfo?.steps[0]?.unitOutPutData?.chunksData, // 拆分后的数据
                            // 第三步页面用的数据
                            ruleFrom:
                              pageInfo?.steps[1]?.unitOutPutData?.ruleFormData,
                            // 最后一步页面用的数据
                            targentData:
                              pageInfo?.steps[2]?.unitOutPutData?.targentData, // 目标的数据
                            rulesData:
                              pageInfo?.steps[2]?.unitOutPutData?.rulesData,
                            originalFile:
                              pageInfo?.pageOutPutData?.originalFile,
                            selectedRules:
                              pageInfo?.steps[2]?.unitOutPutData?.selectedRules,
                          }}
                          pageInfo={{
                            pageName: pageInfo.pageName,
                            pageDesc: pageInfo.pageDesc,
                            pageInputDesc: pageInfo.pageInputDesc,
                            pageOutputDesc: pageInfo.pageOutputDesc,
                            steps: [pageInfo?.steps[currentRef.current]],
                          }}
                        />
                      </div>
                    </>
                  ) : (
                    <div>组件未找到</div>
                  )}
                </Suspense>
              </div>

              <Flex
                justify="center"
                align="center"
                gap={token.marginMD}
                className="legal-review-footer"
              >
                <Button
                  style={{ minWidth: "100px", marginRight: 8 }}
                  onClick={async () => {
                    const ref = refs.current[currentRef.current];
                    const childData = await ref?.current?.triggerSplit?.();
                    setPageInfo((prev) => {
                      const newPageInfo = { ...prev };
                      newPageInfo.steps[currentRef.current].unitOutPutData = {
                        ...newPageInfo.steps[currentRef.current].unitOutPutData,
                        ...childData,
                      };
                      return newPageInfo;
                    });
                    setCurrent(-1);
                  }}
                >
                  返回首页
                </Button>

                {current > 0 && (
                  <Button
                    style={{ minWidth: "100px", marginRight: 8 }}
                    onClick={onPerv}
                  >
                    上一步
                  </Button>
                )}
                <Button
                  icon={<RedoOutlined />}
                  onClick={() => {
                    getSubmit("1");
                  }}
                >
                  重新生成
                </Button>
                <Button
                  icon={<IconFont type="knowledgeBaseOutlined" />}
                  onClick={addKnowledgeBase}
                >
                  保存知识库
                </Button>
                <Button
                  icon={<TeamOutlined />}
                  onClick={async () => {
                    const ref = refs.current[current];
                    const childData = await ref?.current?.triggerSplit?.();
                    setPageInfo((prev) => {
                      const newPageInfo = { ...prev };
                      newPageInfo.steps[current].unitOutPutData = {
                        ...newPageInfo.steps[current].unitOutPutData,
                        ...childData,
                      };
                      return newPageInfo;
                    });
                    setCollOpen(true);
                  }}
                >
                  协同
                </Button>
                {current < pageInfo.steps.length - 1 && (
                  <Button
                    type="primary"
                    onClick={getSubmitInfo}
                    style={{ minWidth: "100px", marginRight: 8 }}
                  >
                    {currentStep?.submitName || "下一步"}
                  </Button>
                )}
                {currentStep?.isExport && (
                  <Button type="primary" onClick={exportBtn}>
                    导出
                  </Button>
                )}
                <Flex className="legal-fixed-right" justify="flex-end">
                  {isReview ? (
                    <span className="legal-fixed-bottom">
                      单元场景{isOutputReview.current ? "输出" : "输入"}
                      评审中...
                    </span>
                  ) : (
                    <Flex vertical>
                      {pageInfo?.steps[current]?.unitOutPutData?.inputData?.[
                        "评审结果"
                      ] && (
                        <span
                          className="legal-fixed-sult"
                          onClick={() => {
                            seeResult(1);
                          }}
                        >
                          <Button
                            type="link"
                            danger={
                              pageInfo?.steps[current]?.unitOutPutData
                                ?.inputData?.["评审结果"] === "异常"
                            }
                            style={
                              pageInfo?.steps[current]?.unitOutPutData
                                ?.inputData?.["评审结果"] === "正常"
                                ? { color: "#52c41a" }
                                : undefined
                            }
                          >
                            输入评审结果：
                            {pageInfo?.steps[current]?.unitOutPutData
                              ?.inputData?.["评审结果"] || "--"}
                          </Button>
                        </span>
                      )}
                      {pageInfo?.steps[current]?.unitOutPutData?.outputData?.[
                        "评审结果"
                      ] && (
                        <span
                          className="legal-fixed-sult"
                          onClick={() => {
                            seeResult(2);
                          }}
                        >
                          <Button
                            type="link"
                            danger={
                              pageInfo?.steps[current]?.unitOutPutData
                                ?.outputData?.["评审结果"] === "异常"
                            }
                            style={
                              pageInfo?.steps[current]?.unitOutPutData
                                ?.outputData?.["评审结果"] === "正常"
                                ? { color: "#52c41a" }
                                : undefined
                            }
                          >
                            输出评审结果：
                            {pageInfo?.steps[current]?.unitOutPutData
                              ?.outputData?.["评审结果"] || "--"}
                          </Button>
                        </span>
                      )}
                    </Flex>
                  )}
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
      <Collaboration
        open={collOpen}
        currentStep={current}
        pageInfo={pageInfo}
        onOk={handleOk}
        onCancel={() => {
          console.log(111);
          setCollOpen(false);
        }}
      />
    </>
  );
};

export default DraftPage;
