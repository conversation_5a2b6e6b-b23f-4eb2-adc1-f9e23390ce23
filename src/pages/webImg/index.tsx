import React, { useEffect, useRef, useState } from "react";
import {
  Layout,
  message,
  Flex,
  theme,
  Image as AntdImage,
  Spin,
  Descriptions,
  Radio,
  Space,
  Button,
  Row,
  Col,
} from "antd";
import HeaderCom from "@/component/Header";
import { getToken, getUserInfo } from "@/utils/auth";
import StreamTypewriter from "@/component/StreamTypewriter";
import useSSEChat from "@/hooks/useSSEChat";
import { fileToBase64 } from "@/utils/common";
import { uploadChatFile } from "@/api/public";
import "./index.less";
const { Content } = Layout;
const { useToken } = theme;
const WebImg = () => {
  const { token } = useToken();
  const sseChat = useSSEChat();
  const pageInfo = {
    pageName: "智能营销生图",
    pageDesc:
      "一键实现模特换装与产品融合，AI智能生成高质量营销图，告别繁琐拍摄，创意无限，营销效率飙升！",
    agentId: "de549851-83ef-42fd-a1ad-225fa157449d",
  };
  const [streamingText, setStreamingText] = useState(""); // 流式的文本
  const scrollRef = useRef<HTMLDivElement>(null); // 滚动的dom
  const [globalLoading, setGlobalLoading] = useState(false); // 全局loading
  const [url, setUrl] = useState(""); // 图片url
  const [uploadedFiles, setUploadedFiles] = useState([]); // 网页图片的上传的文件
  const [selectedImage, setSelectedImage] = useState(null);
  const [description, setDescription] = useState(""); // 描述
  // 模拟图片数据
  const imageList = [
    {
      id: 1,
      url: "/copilot/image/nan.png",
      title: "人物1",
    },
    {
      id: 2,
      url: "/copilot/image/nv.png",
      title: "人物2",
    },
  ];
  // 与插件的通信
  useEffect(() => {
    window.parent.postMessage({ type: "ready" }, "*");

    // 增加标注位，防止handleMessage多次触发
    let flag = false;
    function handleMessage(event: MessageEvent) {
      if (flag) return;
      flag = true;
      // console.log("event", event.data)
      if (event.data?.type === "pageText") {
        console.log("收到页面文本：", event.data.text);
        if (event?.data?.text) {
          uploadFile(event.data.text, 0);
          setUrl(event.data?.text);
        }
        return;
      }
    }

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  async function urlToFile(url: string, fileName = "image.png"): Promise<File> {
    const response = await fetch(url, { mode: "cors" }); // 如果跨域不允许，需要后台代理
    if (!response.ok) throw new Error("无法获取图片: " + response.status);
    const blob = await response.blob();
    return new File([blob], fileName, {
      type: blob.type,
      lastModified: Date.now(),
    });
  }

  /**
   * 压缩图片
   * @param file 原始图片 File
   * @param maxWidth 最大宽度（可选）
   * @param maxHeight 最大高度（可选）
   * @param quality 压缩质量 0~1
   * @returns 压缩后的 File
   */
  async function compressImage(
    file: File,
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 0.8
  ): Promise<File> {
    // 将 File 转成 Image
    const img = new Image();
    img.src = URL.createObjectURL(file);

    await new Promise((resolve, reject) => {
      img.onload = () => resolve(true);
      img.onerror = (err) => reject(err);
    });

    let { width, height } = img;

    // 按比例计算新宽高
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height);
      width = width * ratio;
      height = height * ratio;
    }

    // 创建 canvas 绘制压缩后的图片
    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    if (!ctx) throw new Error("无法获取 canvas 上下文");

    ctx.drawImage(img, 0, 0, width, height);

    // 转成 Blob
    const blob: Blob | null = await new Promise((resolve) =>
      canvas.toBlob(resolve, file.type, quality)
    );

    if (!blob) throw new Error("图片压缩失败");

    // 转成 File
    return new File([blob], file.name, {
      type: file.type,
      lastModified: Date.now(),
    });
  }

  // 文件转base64
  const uploadFile = async (imgUrl: string, type: number) => {
    const userInfo = await getUserInfo();
    const file = await urlToFile(
      imgUrl,
      type === 1 ? "model.png" : "origin.png"
    );
    const fileData = {
      fileName: file.name,
      fileStr: await fileToBase64(file),
      path: "/files/upload",
      agentId: pageInfo.agentId,
      user: userInfo?.id,
      libName: file.name,
      libDesc: "",
      flag: "file",
    };

    const response = await uploadChatFile(fileData);
    if (response.code === 200) {
      const newFiles = [
        ...uploadedFiles.filter((f: any) => f.type !== type),
        { ...response.data, type },
      ];

      // 再更新 state
      setUploadedFiles(newFiles);

      return newFiles; // 一定是最新的
    } else {
      message.error("文件上传失败");
      throw new Error("文件上传失败");
    }
  };
  // const uploadFile = async (imgUrl: any, type?: number) => {
  //   const userInfo = await getUserInfo();
  //   const file = await urlToFile(imgUrl, "image.png");
  //   // const compressedFile = await compressImage(file, 800, 800, 0.7);
  //   const fileData = {
  //     fileName: file.name,
  //     fileStr: await fileToBase64(file),
  //     path: "/files/upload",
  //     agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
  //     user: userInfo?.id,
  //     libName: file.name,
  //     libDesc: "",
  //     flag: "file",
  //   };
  //   uploadChatFile(fileData).then(async (response: any) => {
  //     setGlobalLoading?.(false);
  //     if (response.code == 200) {
  //       console.log(response, 123);
  //       setUploadedFiles([{ ...response.data }]);
  //     } else {
  //       message.open({
  //         key: "uploading",
  //         type: "error",
  //         content: "文件上传失败",
  //         duration: 1,
  //       });
  //     }
  //   });
  // };
  const handleStart = async (type: number) => {
    if (description.trim() === "") {
      message.error("请输入描述信息");
      return;
    }
    if (selectedImage == null) {
      message.error("请选择人物信息");
      return;
    }
    const fileData: any[] = [];
    if (type == 1) {
      uploadedFiles.forEach((item: any) => {
        fileData.push({
          type: "image",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });
    } else {
      const name = selectedImage == 1 ? "nan" : "nv";
      const url = `${import.meta.env.VITE_BASE_API}/copilot/image/${name}.png`;
      const allFiles = await uploadFile(url, 1);
      allFiles.forEach((item: any) => {
        fileData.push({
          type: "image",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });
    }

    setGlobalLoading(true);
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    setStreamingText("");
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: tokenInfo || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: pageInfo.agentId || "",
        agentId: pageInfo.agentId || "",
        path: "/chat-messages",
        difyJson: {
          inputs: {
            key: "123456",
          },
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: description,
          files: fileData,
        },
      },
      query: {},
      onMessage: (text: string) => {
        // 流式更新文本
        setStreamingText(text);
      },
      onFinished: (dataVal: any) => {
        setGlobalLoading(false);
      },
    });
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Layout
        style={{ minHeight: "100vh", background: "#fff" }}
        className="web-img-page"
      >
        <Content>
          <div className="set-question-header">
            {/* 顶部标题和描述 */}
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />
            <div className="page-con">
              <Row
                gutter={24}
                style={{
                  width: streamingText ? "100%" : "800px",
                  margin: "0px auto",
                }}
              >
                {/* 左侧输入面板 */}
                <Col xs={24} md={streamingText ? 12 : 24}>
                  <div style={{ width: "100%" }}>
                    {url && (
                      <Descriptions
                        title={
                          <div
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <div
                              style={{
                                width: 4,
                                height: 16,
                                background: "#1890ff",
                                marginRight: 8,
                                borderRadius: 2,
                              }}
                            />
                            <span>图片信息</span>
                          </div>
                        }
                        column={1}
                        style={{ marginBottom: "24px" }}
                      >
                        <Descriptions.Item>
                          <AntdImage
                            src={url}
                            alt="网图"
                            height={100}
                            style={{ objectFit: "cover" }}
                          />
                        </Descriptions.Item>
                      </Descriptions>
                    )}
                    {/* 图片选择部分 */}
                    <Descriptions
                      title={
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <div
                            style={{
                              width: 4,
                              height: 16,
                              background: "#1890ff",
                              marginRight: 8,
                              borderRadius: 2,
                            }}
                          />
                          <span>人物信息</span>
                        </div>
                      }
                      column={1}
                      style={{ marginBottom: "24px" }}
                    >
                      <Descriptions.Item>
                        <Radio.Group
                          onChange={(e) => setSelectedImage(e.target.value)}
                          value={selectedImage}
                        >
                          <Space>
                            {imageList.map((image) => (
                              <Radio key={image.id} value={image.id}>
                                <Flex vertical justify="center">
                                  <AntdImage
                                    src={image.url}
                                    alt="人物信息"
                                    height={100}
                                    style={{ objectFit: "cover" }}
                                  />
                                  <span style={{ textAlign: "center" }}>
                                    {image.title}
                                  </span>
                                </Flex>
                              </Radio>
                            ))}
                          </Space>
                        </Radio.Group>
                      </Descriptions.Item>
                    </Descriptions>
                    <Descriptions
                      title={
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <div
                            style={{
                              width: 4,
                              height: 16,
                              background: "#1890ff",
                              marginRight: 8,
                              borderRadius: 2,
                            }}
                          />
                          <span>描述信息</span>
                        </div>
                      }
                      column={1}
                      style={{ marginBottom: "24px" }}
                    >
                      <Descriptions.Item>
                        <textarea
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="请输入描述信息"
                          style={{
                            width: "100%",
                            minHeight: "100px",
                            padding: "8px",
                            border: "1px solid #d9d9d9",
                            borderRadius: "8px",
                          }}
                        />
                      </Descriptions.Item>
                    </Descriptions>

                    {/* 提交按钮 */}
                    <Flex justify="center" align="center" gap={token.marginMD}>
                      <div style={{ textAlign: "center", marginTop: "24px" }}>
                        <Button
                          type="primary"
                          onClick={() => {
                            handleStart(2);
                          }}
                        >
                          提交
                        </Button>
                      </div>
                      {streamingText && (
                        <div style={{ textAlign: "center", marginTop: "24px" }}>
                          <Button
                            type="primary"
                            onClick={() => {
                              handleStart(1);
                            }}
                          >
                            重新生成
                          </Button>
                        </div>
                      )}
                    </Flex>
                  </div>
                </Col>
                {streamingText && (
                  <Col xs={24} md={12}>
                    <Descriptions
                      title={
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <div
                            style={{
                              width: 4,
                              height: 16,
                              background: "#1890ff",
                              marginRight: 8,
                              borderRadius: 2,
                            }}
                          />
                          <span>图片识别</span>
                        </div>
                      }
                      column={1}
                    >
                      <Descriptions.Item>
                        {streamingText && (
                          <div
                            style={{
                              width: "100%",
                              border: "1px solid #f0f0f0",
                              borderRadius: 8,
                              padding: 16,
                              background: "#fafafa",
                              height: "calc(100vh - 180px)",
                              overflow: "auto",
                            }}
                            ref={scrollRef}
                          >
                            <StreamTypewriter
                              text={streamingText}
                              onchange={() => {
                                scrollRef.current?.scrollTo({
                                  top: scrollRef.current.scrollHeight,
                                  behavior: "smooth",
                                });
                              }}
                              end={true}
                              charsPerUpdate={5}
                            />
                          </div>
                        )}
                      </Descriptions.Item>
                    </Descriptions>
                  </Col>
                )}
              </Row>
            </div>
          </div>
        </Content>
      </Layout>
    </>
  );
};

export default WebImg;
