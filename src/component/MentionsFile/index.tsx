import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useState,
} from "react";
import type { UploadProps } from "antd";
import "./index.less";
import {
  But<PERSON>,
  Divider,
  Flex,
  Mentions,
  message,
  Spin,
  theme,
  Tooltip,
  Upload,
} from "antd";
import { knowdgeSVGIcon } from "@/assets/config/menu/knowdge";
import { useGetState } from "ahooks";
import { getToken, getUserInfo } from "@/utils/auth";
import classNames from "classnames";
import { uploadChatFile } from "@/api/public";
import {
  CloseCircleFilled,
  FolderAddOutlined,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";

const { useToken } = theme;

interface MentionsComponentProps {
  agentId: string;
  placeholder?: string;
  pageData?: any;
  onQueryChange?: (query: string) => void; // 输入框的值变更
  onFileChange?: (file: any) => void; // 文件发生变化
  setGlobalLoading?: (loading: boolean) => void;
}
export interface MentionsComponentRef {
  getMentionsData: () => object;
  setMentionsData: (data: any) => void;
}
const MentionsComponent = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      agentId,
      onQueryChange,
      onFileChange,
      pageData,
      setGlobalLoading,
      placeholder = "输入要撰写的主题、创作背景、要点",
    },
    ref
  ) => {
    const { token: csstoken } = useToken();
    const [konwTooltipOpen, setKonwTooltipOpen] = useState(false);
    const [localFile, setLocalFile] = useState<any>([]);
    const [allFile, setAllFile] = useState<any>([]);
    const [query, setQuery, getQuery] = useGetState<string>("");
    const [token, setToken] = useState<any>("");
    const [originalFile, setOriginalFile] = useState<any>([]);
    const [dropdownVisible, setDropdownVisible] = useState(false); // 控制Dropdown显示
    const [allFileList, setAllFileList] = useState<number>(0);
    // 是否显示左箭头
    const [isLeftShow, setIsLeftShow] = useState(false);
    // 是否显示右箭头
    const [isRightShow, setIsRightShow] = useState(false);

    // 初始化数据
    useEffect(() => {
      const fetchData = async () => {
        const tokenInfo = await getToken();
        setToken(tokenInfo);
      };
      fetchData();
      if (pageData?.localFile) {
        setLocalFile(pageData?.localFile || []);
        setOriginalFile(pageData?.originalFile || []);
        setAllFile(pageData?.localFile || []);
        setQuery(pageData?.queryData || "");
      }
    }, []);

    const uploadFile: UploadProps = {
      name: "file",
      multiple: true,
      headers: {
        [import.meta.env.VITE_API_HEADER_KEY]: token,
      },
      showUploadList: false,
      accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf",
      beforeUpload(file) {
        if (localFile.length > 4) {
          message.error("最多上传5个附件");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
        const isLt2M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
        if (!isLt2M) {
          message.error("不允许超过15MB!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
        const arr = file.name.split(".");
        const fileName = arr[arr.length - 1] || "";
        const fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
        if (!fileFormat.includes(fileName)) {
          message.error("文件格式不正确!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
      },
    };

    // 文件转base64
    function fileToBase64(file: any) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        // 成功读取文件时的回调
        reader.onload = () => {
          resolve(reader.result); // Base64 编码的字符串
        };

        // 读取文件失败时的回调
        reader.onerror = (error) => {
          reject(error);
        };

        // 读取文件并转为 Base64
        reader.readAsDataURL(file);
      });
    }

    const uploadQueue: any[] = []; // 存储待上传的文件
    let isUploading = false; // 标记是否正在上传

    const processQueue = async () => {
      if (uploadQueue.length === 0) {
        isUploading = false; // 队列为空时，停止上传状态
        return;
      }

      isUploading = true;
      const { file, type } = uploadQueue.shift(); // 取出队列中的第一个任务
      await uploadFileNew(file, type); // 执行上传

      setTimeout(processQueue, 1000); // 1 秒后处理下一个任务
    };

    const handleCustomRequest = async (options: any) => {
      const { file } = options;
      setOriginalFile((prevFiles: any) => [...prevFiles, file]);
      uploadQueue.push({ file, type: 2 }); // 将文件任务加入队列

      if (!isUploading) {
        processQueue(); // 如果当前没有正在上传的任务，启动队列
      }
    };
    const uploadFileMaxCount = () => {
      return allFileList + (5 - localFile.length);
    };

    // 上传文件
    let queue: Promise<void> = Promise.resolve(); // 初始化 Promise 队列

    const uploadFileNew = (file: any, type: any) => {
      queue = queue.then(() => {
        return new Promise((resolve, reject) => {
          let fileData: any = {
            fileName: "",
            fileStr: "",
          };
          if (!file) {
            resolve();
            return;
          }

          // 使用 async/await 来处理异步逻辑
          (async () => {
            setGlobalLoading?.(true);
            const userInfo = await getUserInfo();
            try {
              fileData = {
                fileName: file.name,
                fileStr: await fileToBase64(file),
                loading: true,
                path: "/files/upload",
                agentId,
                user: userInfo?.id,
              };

              if (type == 2) {
                fileData.libName = file.name;
                const arr = file.name.split(".");
                fileData.libDesc = arr[arr.length - 1];
                fileData.flag = "file";
                setLocalFile((prevFiles: any) => {
                  if (
                    prevFiles.some(
                      (item: any) => item.libName === fileData.libName
                    )
                  ) {
                    message.open({
                      type: "warning",
                      content: "已经添加了，不可重复添加",
                    });
                    return prevFiles;
                  }
                  return [...prevFiles, fileData];
                });
              }
            } catch (error) {
              console.error("文件转 Base64 出错：", error);
              reject(); // 发生错误时调用 reject
              return;
            }
            uploadChatFile(fileData).then(async (response) => {
              setGlobalLoading?.(false);
              const res = response.data;
              if (res?.name) {
                if (type == 2) {
                  res.libName = res.name;
                  res.libDesc = res.extension;
                  res.flag = "file";
                  res.fileType = "document";
                  setLocalFile((prevArr: any) =>
                    prevArr.map((item: any) =>
                      item.libName === res.name
                        ? { ...item, ...res, loading: false }
                        : item
                    )
                  );
                }
              } else {
                message.open({
                  type: "error",
                  content: "上传失败",
                });
                setGlobalLoading?.(false);
              }
              resolve(); // 上传完成后调用 resolve
            });
          })();
        });
      });
    };

    const handleDeleteKnowledge = (itemObj, index) => {
      if (itemObj.flag === "file") {
        setLocalFile((prevFiles) => {
          const newFiles = prevFiles.filter((f) => f.id !== itemObj.id);
          onFileChange?.(newFiles); // 确保用最新的文件列表
          return newFiles;
        });
        // 删除 originalFile 中下标和 index 一致的
        setOriginalFile((prevFiles) => prevFiles.filter((_, i) => i !== index));
      }
    };
    const fileExtensionHandler = (item: any) => {
      if (item.libDesc === "pdf") {
        return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
      } else if (item.libDesc === "docx") {
        return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
      } else if (
        item.libDesc === "xls" ||
        item.libDesc === "xlsx" ||
        item.libDesc === "csv"
      ) {
        return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
      } else if (item.libDesc === "txt") {
        return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
      } else if (item.libDesc === "pptx") {
        return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
      }
    };
    useEffect(() => {
      setAllFile([...localFile]);
    }, [localFile]);

    useEffect(() => {
      const timer = setTimeout(() => {
        const div = document.querySelector(".knowledge-base-info");
        if (!div) return;
        const scrollableDistanceX = div.scrollWidth - div.clientWidth;
        if (scrollableDistanceX > 0) {
          // 是否有滚动条
          if (div.scrollLeft > 0) {
            // 距离左侧是否有距离
            setIsRightShow(true);
            setIsLeftShow(true);
          } else {
            setIsRightShow(true);
            setIsLeftShow(false);
          }
        } else {
          setIsRightShow(false);
          setIsLeftShow(false);
        }
      }, 300);
      return () => clearTimeout(timer);
    }, [localFile]);
    // 点击左右箭头
    const translateX = (type: number) => {
      const div: any = document.querySelector(".knowledge-base-info");
      const scrollableDistanceX = div.scrollWidth - div.clientWidth;
      if (type === 1) {
        if (div.scrollLeft + 114 > scrollableDistanceX) {
          if (scrollableDistanceX > 0) {
            setIsRightShow(false);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: scrollableDistanceX,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        } else {
          if (scrollableDistanceX > 0) {
            setIsRightShow(true);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: div.scrollLeft + 114,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        }
      } else {
        if (div.scrollLeft - 114 < 0) {
          if (scrollableDistanceX > 0) {
            setIsLeftShow(false);
            setIsRightShow(true);
          }
          div.scrollTo({
            left: 0,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        } else {
          if (scrollableDistanceX > 0) {
            setIsRightShow(true);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: div.scrollLeft - 114,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        }
      }
    };

    // 将这个方法吐出，
    useImperativeHandle(ref, () => ({
      getMentionsData: () => {
        return {
          localFile,
          originalFile: originalFile,
          query: getQuery(),
        };
      },
      setMentionsData: (data: any) => {
        if (data.localFile !== undefined) setLocalFile(data.localFile);
      },
    }));
    return (
      <Flex className="mentions-components">
        <Flex className="text-input" vertical>
          <Flex className="chat-textarea" vertical>
            <Mentions
              prefix="/"
              autoFocus
              className="text-input-mentions"
              placeholder={placeholder}
              rows={4}
              value={query}
              maxLength={10000}
              options={[]}
              onInput={(e) => {
                let value = (e.target as HTMLInputElement).value;
                // 检查内容是否只包含空格或回车符
                if (/^\s*$/.test(value)) {
                  setQuery(""); // 如果只包含空格或回车符，清空输入框
                } else {
                  setQuery(value); // 否则更新输入内容
                }
                onQueryChange?.(value); // 通知父组件
              }}
              onSearch={(text) => {
                if (dropdownVisible) {
                  setDropdownVisible(false);
                }
              }}
            />
            <Flex vertical style={{ padding: "0px 12px 12px" }}>
              <Divider
                orientation="left"
                style={{
                  color: csstoken.colorTextTertiary,
                  fontSize: "10px",
                  margin: "0px",
                  fontWeight: "normal",
                }}
              >
                素材参考
              </Divider>
              <Flex
                className="top-toolbar"
                justify="space-between"
                align="center"
              >
                <Flex className="top-toolbar-left" align="center">
                  <Flex align="center" gap={csstoken.marginXXS}>
                    <Flex className="upload-file">
                      <Tooltip
                        placement="top"
                        open={konwTooltipOpen}
                        onOpenChange={setKonwTooltipOpen}
                        title={
                          <Flex>
                            <ul
                              style={{ margin: 0, padding: 0, width: "100%" }}
                            >
                              <li>支持：docx,pptx,xls,xlsx,csv,txt,pdf</li>
                              <li>限制：单个文件15MB;最多5个附件</li>
                            </ul>
                          </Flex>
                        }
                      >
                        <Upload
                          {...uploadFile}
                          maxCount={uploadFileMaxCount()}
                          customRequest={handleCustomRequest}
                          className="sino-upload-file"
                        >
                          <FolderAddOutlined className="icon" />
                        </Upload>
                      </Tooltip>
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
              <Flex
                className={classNames({ "knowledge-base": allFile.length > 0 })}
              >
                {allFile && (isRightShow || isLeftShow) && (
                  <Flex>
                    {isRightShow && (
                      <Flex className="right-icon">
                        <Button
                          shape="circle"
                          icon={<RightOutlined />}
                          className="right"
                          onClick={() => translateX(1)}
                        />
                      </Flex>
                    )}
                    {isLeftShow && (
                      <Flex className="left-icon">
                        <Button
                          shape="circle"
                          icon={<LeftOutlined />}
                          className="left"
                          onClick={() => translateX(2)}
                        />
                      </Flex>
                    )}
                  </Flex>
                )}
                <Flex className="knowledge-base-info">
                  {allFile &&
                    allFile.map((item, index) => {
                      return (
                        <Flex
                          key={index}
                          className={classNames("knowledge-content-base", {
                            "knowledge-content-base-width":
                              allFile.length > 1 && !item.loading,
                          })}
                        >
                          {item.loading && (
                            <Flex className="knowledge-load" align="center">
                              <Spin spinning={item.loading}></Spin>
                            </Flex>
                          )}
                          <Flex
                            className="knowledge-content-base-flex"
                            align="center"
                            style={{
                              width: !item.loading
                                ? "100%"
                                : "calc(100% - 28px)",
                            }}
                          >
                            <Flex
                              className="knowledge-content-base-item"
                              align="center"
                            >
                              {item.flag === "file" &&
                                fileExtensionHandler(item)}
                              <Flex
                                className="knowledge-base-item first-title"
                                vertical
                                justify="center"
                              >
                                <div className="knowledge-base-title">
                                  {item.libName}
                                </div>
                                <div className="two-title">{item.libDesc}</div>
                              </Flex>
                            </Flex>
                            {!item.loading && (
                              <CloseCircleFilled
                                className="close"
                                onClick={() =>
                                  handleDeleteKnowledge(item, index)
                                }
                              />
                            )}
                          </Flex>
                        </Flex>
                      );
                    })}
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    );
  }
);
// 添加 displayName
MentionsComponent.displayName = "MentionsComponent";
export default MentionsComponent;
