/* DocX预览组件样式修复 - 解决多重滚动条和闪动问题 */

/* 全局docx预览容器样式 */
.my-docx-preview,
.my-split-docx-preview {
  /* 确保只有一个滚动容器 */
  overflow: auto !important;
  
  /* 防止闪动 */
  transition: none !important;
  animation: none !important;
  
  /* 确保内部元素不产生额外滚动条 */
  .docx-wrapper {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
    
    /* 防止闪动 */
    transition: none !important;
    animation: none !important;
    
    section.docx {
      overflow: visible !important;
      height: auto !important;
      max-height: none !important;
      
      /* 防止闪动 */
      transition: none !important;
      animation: none !important;
      
      /* 确保所有子元素不产生滚动条 */
      * {
        &:not(.my-docx-preview):not(.my-split-docx-preview) {
          overflow: visible !important;
          max-height: none !important;
        }
      }
      
      /* 图片和媒体元素优化 */
      img, svg, canvas, video {
        transition: none !important;
        animation: none !important;
        max-width: 100% !important;
        height: auto !important;
      }
      
      /* 表格优化 */
      table {
        transition: none !important;
        animation: none !important;
        width: 100% !important;
        table-layout: auto !important;
      }
      
      /* 段落和文本优化 */
      p, div, span {
        transition: none !important;
        animation: none !important;
      }
    }
  }
  
  /* 高亮样式优化 */
  .docx-highlight {
    background-color: #ffeb3b !important;
    padding: 2px 4px !important;
    border-radius: 2px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    transition: background-color 0.2s ease !important; /* 只保留背景色过渡 */

    &:hover {
      background-color: #ffc107 !important;
    }
  }
}

/* 特定组件的样式修复 */
.law-review {
  .my-split-docx-preview {
    /* 确保在法律审查组件中的正确显示 */
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
    
    /* 移除可能的边距和内边距冲突 */
    margin: 0 !important;
    padding: 0 !important;
    
    /* 确保内容正确显示 */
    .docx-wrapper {
      padding: 20px !important;
      background: transparent !important;
    }
  }
}

/* 分割预览模块样式修复 */
.split-preview {
  .my-split-docx-preview {
    /* 确保在分割预览组件中的正确显示 */
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
    
    .docx-wrapper {
      padding: 20px !important;
      background: transparent !important;
    }
  }
}

/* 目标定位模块样式修复 */
.targeting-module {
  .my-split-docx-preview {
    /* 确保在目标定位组件中的正确显示 */
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
    
    .docx-wrapper {
      padding: 20px !important;
      background: transparent !important;
    }
  }
}

/* 防止docx-preview库的默认样式干扰 */
.docx {
  /* 重置可能导致滚动条问题的样式 */
  overflow: visible !important;
  max-height: none !important;
  height: auto !important;
  
  /* 防止闪动 */
  transition: none !important;
  animation: none !important;
  
  /* 确保文档内容正确显示 */
  .docx-wrapper {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
  }
}

/* 滚动条样式优化 */
.my-docx-preview::-webkit-scrollbar,
.my-split-docx-preview::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.my-docx-preview::-webkit-scrollbar-track,
.my-split-docx-preview::-webkit-scrollbar-track {
  background: rgba(200, 200, 200, 0.1);
  border-radius: 4px;
}

.my-docx-preview::-webkit-scrollbar-thumb,
.my-split-docx-preview::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.my-docx-preview::-webkit-scrollbar-thumb:hover,
.my-split-docx-preview::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 120, 0.8);
}

/* Firefox滚动条样式 */
@supports (scrollbar-color: red blue) {
  .my-docx-preview,
  .my-split-docx-preview {
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.5) transparent;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .my-docx-preview,
  .my-split-docx-preview {
    /* 移动端隐藏滚动条 */
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
