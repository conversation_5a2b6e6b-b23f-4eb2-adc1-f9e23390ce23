import { useEffect, useRef, forwardRef, useImperative<PERSON>andle } from "react";
import { renderAsync } from "docx-preview";
import "./docx-preview-fix.less";

// 控制日志输出的工具函数
const logOnce = (() => {
  const logged = new Set();
  return (message: string, key?: string) => {
    const logKey = key || message;
    if (!logged.has(logKey)) {
      console.log(message);
      logged.add(logKey);
    }
  };
})();

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null; // 支持 File / Blob / ArrayBuffer
  className?: string; // 自定义 className
  ignoreCss?: boolean; // 是否忽略 docx-preview 自带样式
  style?: React.CSSProperties; // 容器样式
  highlightWords?: string[]; // 需要高亮的文字数组
  onHighlightScroll?: (word: string) => void; // 高亮滚动回调
}

export interface DocxPreviewRef {
  highlightAndScroll: (word: string) => void;
  isHighlighted: (word: string) => boolean;
  clearHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px" },
      highlightWords = [],
      onHighlightScroll,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const currentHighlights = useRef<string[]>([]);

    // 清除所有高亮
    const clearHighlights = () => {
      if (!previewRef.current) return;

      const highlightedElements =
        previewRef.current.querySelectorAll(".docx-highlight");
      highlightedElements.forEach((el) => {
        const parent = el.parentNode;
        if (parent) {
          parent.replaceChild(
            document.createTextNode(el.textContent || ""),
            el
          );
          parent.normalize(); // 合并相邻的文本节点
        }
      });
      currentHighlights.current = [];
    };

    // 高亮指定文字
    const highlightText = (word: string) => {
      if (!previewRef.current || !word.trim()) return false;

      const walker = document.createTreeWalker(
        previewRef.current,
        NodeFilter.SHOW_TEXT,
        null
      );

      const textNodes: Text[] = [];
      let node;
      while ((node = walker.nextNode())) {
        textNodes.push(node as Text);
      }

      let found = false;
      textNodes.forEach((textNode) => {
        const text = textNode.textContent || "";
        const index = text.toLowerCase().indexOf(word.toLowerCase());

        if (index !== -1) {
          found = true;
          const beforeText = text.substring(0, index);
          const matchText = text.substring(index, index + word.length);
          const afterText = text.substring(index + word.length);

          const fragment = document.createDocumentFragment();

          if (beforeText) {
            fragment.appendChild(document.createTextNode(beforeText));
          }

          const highlightSpan = document.createElement("span");
          highlightSpan.className = "docx-highlight";
          highlightSpan.textContent = matchText;
          highlightSpan.style.backgroundColor = "#ffeb3b";
          highlightSpan.style.padding = "2px 4px";
          highlightSpan.style.borderRadius = "2px";
          highlightSpan.setAttribute("data-highlight-word", word);
          fragment.appendChild(highlightSpan);

          if (afterText) {
            fragment.appendChild(document.createTextNode(afterText));
          }

          textNode.parentNode?.replaceChild(fragment, textNode);
        }
      });

      return found;
    };

    // 滚动到高亮位置
    const scrollToHighlight = (word: string) => {
      if (!previewRef.current) return;

      const highlightElement = previewRef.current.querySelector(
        `[data-highlight-word="${word}"]`
      );
      if (highlightElement) {
        highlightElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
        onHighlightScroll?.(word);
      }
    };

    // 检查是否已高亮
    const isHighlighted = (word: string): boolean => {
      return currentHighlights.current.includes(word);
    };

    // 高亮并滚动
    const highlightAndScroll = (word: string) => {
      if (!word.trim()) return;

      if (isHighlighted(word)) {
        // 如果已经高亮，直接滚动
        scrollToHighlight(word);
      } else {
        // 如果未高亮，先高亮再滚动
        const success = highlightText(word);
        if (success) {
          currentHighlights.current.push(word);
          setTimeout(() => scrollToHighlight(word), 100); // 稍微延迟确保DOM更新完成
        }
      }
    };

    useImperativeHandle(ref, () => ({
      highlightAndScroll,
      isHighlighted,
      clearHighlights,
    }));

    useEffect(() => {
      if (!file || !previewRef.current) return;

      // 清空上一次渲染内容，避免多个文件叠加
      previewRef.current.innerHTML = "";
      currentHighlights.current = [];

      renderAsync(file, previewRef.current, undefined, {
        className,
        ignoreFonts: true, // 忽略字体，减少渲染闪动
      })
        .then(() => {
          // DOCX 渲染成功，避免重复日志输出

          // 添加稳定性处理，避免闪动和多重滚动
          if (previewRef.current) {
            // 确保容器样式稳定
            const docxWrapper =
              previewRef.current.querySelector(".docx-wrapper");
            if (docxWrapper) {
              (docxWrapper as HTMLElement).style.overflow = "visible";
              (docxWrapper as HTMLElement).style.height = "auto";
              (docxWrapper as HTMLElement).style.maxHeight = "none";
            }

            // 确保docx内容区域样式稳定
            const docxSection =
              previewRef.current.querySelector("section.docx");
            if (docxSection) {
              (docxSection as HTMLElement).style.overflow = "visible";
              (docxSection as HTMLElement).style.height = "auto";
              (docxSection as HTMLElement).style.maxHeight = "none";
            }

            // 移除可能的内部滚动容器
            const scrollContainers = previewRef.current.querySelectorAll(
              '[style*="overflow"]'
            );
            scrollContainers.forEach((container) => {
              if (container !== previewRef.current) {
                (container as HTMLElement).style.overflow = "visible";
              }
            });
          }

          // 渲染完成后，延迟应用高亮词，避免闪动
          setTimeout(() => {
            highlightWords.forEach((word) => {
              if (word.trim()) {
                const success = highlightText(word);
                if (success && !currentHighlights.current.includes(word)) {
                  currentHighlights.current.push(word);
                }
              }
            });
          }, 100);
        })
        .catch((err) => console.error("DOCX 渲染失败:", err));
    }, [file, className]);

    // 监听 highlightWords 变化
    useEffect(() => {
      if (!previewRef.current) return;

      // 清除所有高亮
      clearHighlights();

      // 重新应用高亮
      highlightWords.forEach((word) => {
        if (word.trim()) {
          const success = highlightText(word);
          if (success && !currentHighlights.current.includes(word)) {
            currentHighlights.current.push(word);
          }
        }
      });
    }, [highlightWords]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

export default DocxPreview;
