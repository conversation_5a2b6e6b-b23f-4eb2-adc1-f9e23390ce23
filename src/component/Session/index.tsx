import { memo, useState, useRef } from "react";
import "./index.less";
import {
  Flex,
  Space,
  Button,
  Input,
  Avatar,
  Tag,
  Upload,
  Form,
  DatePicker,
  Select,
  Radio,
  message,
} from "antd";
import {
  UserOutlined,
  VideoCameraFilled,
  CloseOutlined,
  RedoOutlined,
  CheckCircleFilled,
  InboxOutlined,
  UploadOutlined,
  SmileOutlined,
  AreaChartOutlined,
  Pie<PERSON><PERSON>Outlined,
  Bar<PERSON>hartOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { Message } from "@/types/session";
import dai from "@/assets/images/customer/dai1.png";
import dai2 from "@/assets/images/customer/dai2.png";
import dai3 from "@/assets/images/customer/dia3.png";
import { fileToBase64 } from "@/utils/common";
import { getUserInfo } from "@/utils/auth";
import { uploadChatFile } from "@/api/public";
import dayjs from "dayjs";
import StreamTypewriter from "@/component/StreamTypewriter";

const Session = ({
  messageList,
  submitMessage,
  sessionBtnList,
  currentUser,
  messageBtn,
  sessionRef,
  uploadFile,
  submitForm,
  sessionBtn,
  productList,
  showLoading,
  marketScript,
  deleteMessage,
  markeDowanStatus,
  changeMarkeDownStatus,
}: any) => {
  const [msgContent, setMsgContent] = useState("");
  const [curMessionBtnId, setCurSessionBtnId] = useState("");
  const [showMarketingScript, setShowMarketingScript] = useState(false);
  // const scrollRef = useRef<HTMLDivElement>(null);
  const submitMsg = (e: any) => {
    console.log("submitMsg", e);
    if (e.code == "Enter") {
      if (e.shiftKey) {
        console.log("11111111");
        return;
      } else {
        console.log("2222222");
        e.preventDefault();
        submitMessage(msgContent);
        setMsgContent("");
      }
    } else {
      submitMessage(msgContent);
      setMsgContent("");
    }
  };
  const selectSessionBtn = (id: string) => {
    setShowMarketingScript(true);
    setCurSessionBtnId(id);
    sessionBtn(id);
    setTimeout(() => {
      const btnList = document.querySelector(".marketing-script");
      // for (let i = 0; i < btnList.length; i++) {
      //   btnList[i].classList.remove("move-up-div");
      // }
      btnList?.classList.add("move-up-div");
    });
  };
  const closeSessionScript = () => {
    const btnList = document.querySelector(".marketing-script");
    btnList?.classList.add("move-down-div");
    btnList?.classList.remove("move-up-div");
    setTimeout(() => {
      setShowMarketingScript(false);
    }, 700);
    setCurSessionBtnId("");
  };
  const clickMessageBtn = (btnName: string) => {
    // setMsgContent("用户已选择" + btnName);
    messageBtn(btnName);
  };
  const [uploadedFiles] = useState([]);
  // 上传文件
  const beforeUpload = async (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    // setGlobalLoading?.(true);
    if (["docx", "doc", "pdf", "txt", "md"].includes(originalFileExt)) {
      const userInfo = await getUserInfo();
      const fileData = {
        fileName: file.name,
        fileStr: await fileToBase64(file),
        path: "/files/upload",
        agentId: "13455053-e34d-417e-b0fd-30e664c93e71",
        user: userInfo?.id,
        libName: file.name,
        libDesc: "",
        flag: "file",
      };
      showLoading("1");
      uploadChatFile(fileData).then(async (response: any) => {
        // setGlobalLoading?.(false);
        if (response.code == 200) {
          console.log(response, 4444);
          const uploadData = response.data;
          uploadData.uploadTime = dayjs().format("YYYY-MM-DD");
          uploadFile(uploadData);
          message.open({
            key: "uploading",
            type: "success",
            content: "文件上传成功",
            duration: 1,
          });
          showLoading("0");
        } else {
          message.open({
            key: "uploading",
            type: "error",
            content: "文件上传失败",
            duration: 1,
          });
          showLoading("0");
        }
      });
    }
  };
  const onFinish = (values: any) => {
    console.log("Success:", values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };
  const handleChange = (value: string) => {
    console.log(`selected ${value}`);
  };
  // -----表单-----
  // 自营贷款类型
  const [loanType, setLoanType] = useState(1);
  const changeLoanType = (e: any) => {
    setLoanType(e.target.value);
  };
  // 业务类型
  const [businessTypeList] = useState([
    {
      id: "1",
      name: "自营贷款",
    },
    {
      id: "2",
      name: "委托贷款",
    },
    {
      id: "3",
      name: "承兑业务",
    },
    {
      id: "4",
      name: "保理业务",
    },
  ]);
  const [currentBusinessType, setCurrentBusinessType] = useState("1");
  const changeBusinessType = (id: string) => {
    setCurrentBusinessType(id);
  };
  const submitFormData = () => {
    submitForm();
  };
  return (
    <>
      <Flex vertical={true} className="session">
        <Flex justify="space-between" align="center" className="session-title">
          <Flex vertical={true} className="title">
            <Flex align="center" gap="15px">
              <Flex>
                {currentUser == "2"
                  ? "北京某某某科技有限公司"
                  : "科大讯飞股份有限公司"}
              </Flex>
              <Flex style={{ height: 20 }}>
                <Tag color="#EFFDF4" bordered={false}>
                  <span style={{ color: "#1EAF52", fontSize: 10 }}>
                    AI 模式
                  </span>
                </Tag>
              </Flex>
            </Flex>
          </Flex>
          <Space size="small" className="header-space">
            <Button type="primary">AI 接管</Button>
            <Button>人工介入</Button>
            <Button>
              <VideoCameraFilled />
              在线会议
            </Button>
            {currentUser == "2" && (
              <Button onClick={deleteMessage}>关闭</Button>
            )}
          </Space>
        </Flex>
        <Flex className="session-content">
          <Flex
            ref={sessionRef}
            className="session-content-message"
            vertical={true}
          >
            {messageList.map((item: Message) => (
              <Flex key={item.id}>
                {item.msgBelong !== currentUser && (
                  <Flex className="message-item" vertical={true}>
                    <Flex>
                      <Avatar shape="square" icon={<UserOutlined />} />
                      <Flex className="message-desc" vertical={true}>
                        {item.type?.type == "form" && (
                          <Flex style={{ whiteSpace: "break-spaces" }}>
                            {item.msg}
                          </Flex>
                        )}
                        {item.type?.type !== "form" && (
                          <Flex style={{ whiteSpace: "break-spaces" }}>
                            {markeDowanStatus.includes(item.id) ? (
                              <Flex> {item.msg}</Flex>
                            ) : (
                              <StreamTypewriter
                                key={item.id}
                                text={item.msg}
                                end={true}
                                onchange={() => {
                                  sessionRef.current?.scrollTo({
                                    top: sessionRef.current.scrollHeight,
                                    behavior: "smooth",
                                  });
                                }}
                                onFinished={() => {
                                  console.log("9999iiiikkkk");
                                  changeMarkeDownStatus(item.id);
                                }}
                              />
                            )}
                          </Flex>
                        )}
                        {item.type?.type == "button" && (
                          <Flex className="message-business-btn">
                            <Space>
                              {item.type.list &&
                                item.type.list.map((btn, index) => (
                                  <Button
                                    key={index}
                                    size="small"
                                    onClick={() => clickMessageBtn(btn)}
                                  >
                                    {btn}
                                  </Button>
                                ))}
                            </Space>
                          </Flex>
                        )}
                        {item.type?.type == "upload" && (
                          <Flex className="message-upload">
                            <Upload.Dragger
                              showUploadList={false}
                              multiple={true}
                              beforeUpload={beforeUpload}
                              accept=".docx,.doc,.pdf,.txt,.md"
                              fileList={uploadedFiles}
                            >
                              <div className="ant-upload-drag-icon">
                                {uploadedFiles.length > 0 ? (
                                  <CheckCircleFilled />
                                ) : (
                                  <InboxOutlined />
                                )}
                              </div>
                              <p className="ant-upload-hint">
                                <span>点击或将文件拖到此处上传</span>
                                <span>支持doc,docx,txt,pdf格式文件</span>
                              </p>
                            </Upload.Dragger>
                          </Flex>
                        )}
                        {item.type?.type == "form" && (
                          <Flex className="message-form" vertical={true}>
                            <Flex
                              className="message-form-title"
                              flex={1}
                              justify="center"
                            >
                              企业贷款业务申请表
                            </Flex>
                            <Flex className="message-form-type">
                              请您选择需要办理的业务类型:
                            </Flex>
                            <Flex className="message-form-type-btns">
                              <Space>
                                {businessTypeList.map((item: any) => (
                                  <Flex
                                    key={item.id}
                                    // className="message-form-btn-item message-form-btn-item-active"
                                    className={
                                      item.id == currentBusinessType
                                        ? "message-form-btn-item message-form-btn-item-active"
                                        : "message-form-btn-item"
                                    }
                                    onClick={() => changeBusinessType(item.id)}
                                  >
                                    {item.name}
                                  </Flex>
                                ))}
                              </Space>
                            </Flex>
                            <Flex className="message-form-type">
                              企业基本信息:
                            </Flex>
                            <Flex className="message-form-info">
                              <Form
                                // name="basic"
                                // labelCol={{ span: 8 }}
                                // wrapperCol={{ span: 16 }}
                                style={{ width: "100%" }}
                                initialValues={{ remember: true }}
                                onFinish={onFinish}
                                onFinishFailed={onFinishFailed}
                                autoComplete="off"
                              >
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业名称"
                                        name="companyName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业名称",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业名称" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="统一社会信用代码"
                                        name="uscc"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入统一社会信用代码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入统一社会信用代码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业法定代表人姓名"
                                        name="legalName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业法定代表人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业法定代表人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业联系电话"
                                        name="companyNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业联系电话",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业联系电话" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业注册地址"
                                        name="address"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业注册地址",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业注册地址" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业成立日期"
                                        name="incorporationDate"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择企业成立日期",
                                          },
                                        ]}
                                      >
                                        <DatePicker style={{ width: "100%" }} />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex className="message-form-type">
                                  贷款信息
                                </Flex>
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="申请金额(万元)"
                                        name="amount"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入申请金额",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入申请金额" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="期望贷款期限"
                                        name="loanTerm"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择期望贷款期限",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择期望贷款期限"
                                          options={[
                                            { value: "six", label: "6个月" },
                                            { value: "oneYear", label: "1年" },
                                            {
                                              value: "twoYear",
                                              label: "2年",
                                            },
                                            {
                                              value: "threeYear",
                                              label: "3年",
                                            },
                                          ]}
                                          onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="还款方式"
                                        name="repaymentMethod"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择还款方式",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择还款方式"
                                          options={[
                                            { value: "1", label: "等额本金" },
                                            { value: "2", label: "等额本息" },
                                          ]}
                                          onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  style={{ width: "100%", marginBottom: 20 }}
                                  vertical={true}
                                >
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={4}>
                                      <Form.Item
                                        label="自营贷款类型"
                                        name="selfLoanTypes"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入自营贷款类型",
                                          },
                                        ]}
                                      >
                                        <Radio.Group
                                          value={loanType}
                                          onChange={changeLoanType}
                                          style={{
                                            display: "flex",
                                            flexDirection: "column",
                                            gap: 8,
                                          }}
                                          options={[
                                            {
                                              value: 1,
                                              label:
                                                "固定资产贷款（如厂房、设备购置）",
                                            },
                                            {
                                              value: 2,
                                              label:
                                                "流动资产贷款（如原材料采购、支付账款）",
                                            },
                                          ]}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={3}>
                                      <Form.Item
                                        label="贷款用途说明"
                                        name="loanPurpose"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入贷款用途说明",
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          placeholder="例如：用于购买一个新的生产线"
                                          rows={2}
                                          autoSize={{ minRows: 4, maxRows: 4 }}
                                        ></Input.TextArea>
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  className="message-form-type"
                                  style={{
                                    marginTop: 65,
                                  }}
                                >
                                  联系人信息
                                </Flex>{" "}
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人姓名"
                                        name="contactName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人手机号码"
                                        name="contactNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人手机号码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人手机号码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人电子邮箱"
                                        name="contactEmail"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人电子邮箱",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人电子邮箱" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人职务"
                                        name="contactPosition"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人职务",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人职务" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex flex={1}>
                                  <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    onClick={submitFormData}
                                  >
                                    提交
                                  </Button>
                                </Flex>
                              </Form>
                            </Flex>
                          </Flex>
                        )}
                        {item.type?.type == "product" && (
                          <Flex className="message-product" wrap>
                            {item.type.productList.length > 0 &&
                              item.type.productList.map(
                                (item: any, index: number) => (
                                  <Flex
                                    key={index}
                                    className="message-product-item"
                                    flex={"30%"}
                                    vertical={true}
                                  >
                                    <Flex className="message-product-item-img">
                                      <img
                                        style={{
                                          width: "100%",
                                          height: "100%",
                                          borderRadius: 5,
                                        }}
                                        src={
                                          index == 0
                                            ? dai
                                            : index == 1
                                            ? dai2
                                            : dai3
                                        }
                                      />
                                    </Flex>
                                    <Flex className="message-product-item-name">
                                      <Flex className="message-product-item-title">
                                        {item.productName}
                                      </Flex>
                                      {/* <Flex flex={1} justify="end">
                                        <Tag
                                          color="blue"
                                          bordered={false}
                                          style={{
                                            marginRight: 0,
                                            fontSize: 12,
                                          }}
                                        >
                                          {item.type}
                                        </Tag>
                                      </Flex> */}
                                    </Flex>
                                    <Flex className="message-product-item-desc">
                                      {item.productDescription}
                                    </Flex>
                                    <Flex
                                      className="message-product-item-card"
                                      gap={10}
                                    >
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        justify="center"
                                        align="center"
                                        flex={1}
                                      >
                                        <Flex>
                                          <AreaChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最高额度
                                        </Flex>
                                        <Flex
                                          className="message-product-card-item-price"
                                          justify="center"
                                          style={{ textAlign: "center" }}
                                        >
                                          {item.coreElements.quota}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <PieChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最长期限
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.term}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <BarChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          担保方式
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.guarantee}
                                        </Flex>
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-hot"
                                      vertical={true}
                                    >
                                      <Flex className="message-product-hot-name">
                                        产品亮点
                                      </Flex>
                                      <Flex
                                        className="message-product-hot-list"
                                        vertical={true}
                                        justify="start"
                                      >
                                        {Array.isArray(
                                          item.productHighlights
                                        ) &&
                                          item.productHighlights.map(
                                            (item: string, index: number) => (
                                              <Flex
                                                key={index}
                                                align="center"
                                                className="message-product-hot-list-item"
                                              >
                                                <Flex>
                                                  <CheckCircleOutlined
                                                    style={{
                                                      background: "#2B7FFE",
                                                      borderRadius: "50%",
                                                      color: "#fff",
                                                      fontSize: 15,
                                                      marginRight: 5,
                                                      height: 15,
                                                    }}
                                                  />
                                                </Flex>
                                                <Flex className="message-product-hot-list-desc">
                                                  {item}
                                                </Flex>
                                              </Flex>
                                            )
                                          )}
                                        {typeof item.productHighlights ==
                                          "string" && (
                                          <Flex
                                            key={index}
                                            align="center"
                                            className="message-product-hot-list-item"
                                          >
                                            <Flex>
                                              <CheckCircleOutlined
                                                style={{
                                                  background: "#2B7FFE",
                                                  borderRadius: "50%",
                                                  color: "#fff",
                                                  fontSize: 15,
                                                  marginRight: 5,
                                                  height: 15,
                                                }}
                                              />
                                            </Flex>
                                            <Flex
                                              className="message-product-hot-list-desc"
                                              flex={1}
                                            >
                                              {item.productHighlights}
                                            </Flex>
                                          </Flex>
                                        )}
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-btn"
                                      flex={1}
                                      justify="end"
                                    >
                                      <Button
                                        type="primary"
                                        size="small"
                                        style={{ marginTop: "auto" }}
                                      >
                                        立即申请
                                      </Button>
                                    </Flex>
                                  </Flex>
                                )
                              )}
                          </Flex>
                        )}
                      </Flex>
                    </Flex>
                    <Flex
                      className="message-time"
                      flex={1}
                      justify="start"
                      style={{ paddingLeft: 42 }}
                    >
                      {item.time}
                    </Flex>
                    {item.first && (
                      <Flex
                        className="message-btn"
                        justify="end"
                        style={{ marginRight: 10 }}
                      >
                        <Space>
                          <Button>人工介入</Button>
                          <Button>在线语音</Button>
                        </Space>
                      </Flex>
                    )}
                  </Flex>
                )}
                {item.msgBelong == currentUser && (
                  <Flex
                    className="message-item message-item-right"
                    vertical={true}
                  >
                    <Flex>
                      <Flex
                        className="message-desc"
                        vertical={true}
                        style={{
                          background:
                            item.type?.type == "form" ||
                            item.type?.type == "upload" ||
                            item.type?.type == "product"
                              ? "#fff"
                              : "",
                          color:
                            item.type?.type == "form" ||
                            item.type?.type == "upload" ||
                            item.type?.type == "product"
                              ? "#000"
                              : "",
                        }}
                      >
                        <Flex style={{ whiteSpace: "break-spaces" }}>
                          {item.msg}
                        </Flex>
                        {item.type?.type == "button" && (
                          <Flex className="message-business-btn">
                            <Space>
                              {item.type.list &&
                                item.type.list.map((btn, index) => (
                                  <Button
                                    key={index}
                                    size="small"
                                    onClick={() => clickMessageBtn(btn)}
                                  >
                                    {btn}
                                  </Button>
                                ))}
                            </Space>
                          </Flex>
                        )}
                        {item.type?.type == "upload" && (
                          <Flex className="message-upload">
                            <Upload.Dragger
                              showUploadList={false}
                              multiple={true}
                              beforeUpload={beforeUpload}
                              accept=".docx,.doc,.pdf,.txt,.md"
                              fileList={uploadedFiles}
                            >
                              <div className="ant-upload-drag-icon">
                                {uploadedFiles.length > 0 ? (
                                  <CheckCircleFilled />
                                ) : (
                                  <InboxOutlined />
                                )}
                              </div>
                              <p className="ant-upload-hint">
                                <span>点击或将文件拖到此处上传</span>
                                <span>支持doc,docx,txt,pdf格式文件</span>
                              </p>
                            </Upload.Dragger>
                          </Flex>
                        )}
                        {item.type?.type == "form" && (
                          <Flex className="message-form" vertical={true}>
                            <Flex
                              className="message-form-title"
                              flex={1}
                              justify="center"
                            >
                              企业贷款业务申请表
                            </Flex>
                            <Flex className="message-form-type">
                              请您选择需要办理的业务类型:
                            </Flex>
                            <Flex className="message-form-type-btns">
                              <Space>
                                {businessTypeList.map((item: any) => (
                                  <Flex
                                    key={item.id}
                                    // className="message-form-btn-item message-form-btn-item-active"
                                    className={
                                      item.id == currentBusinessType
                                        ? "message-form-btn-item message-form-btn-item-active"
                                        : "message-form-btn-item"
                                    }
                                    onClick={() => changeBusinessType(item.id)}
                                  >
                                    {item.name}
                                  </Flex>
                                ))}
                              </Space>
                            </Flex>
                            <Flex className="message-form-type">
                              企业基本信息:
                            </Flex>
                            <Flex className="message-form-info">
                              <Form
                                name="basic"
                                // labelCol={{ span: 8 }}
                                // wrapperCol={{ span: 16 }}
                                style={{ width: "100%" }}
                                initialValues={{ remember: true }}
                                onFinish={onFinish}
                                onFinishFailed={onFinishFailed}
                                autoComplete="off"
                              >
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业名称"
                                        name="companyName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业名称",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业名称" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="统一社会信用代码"
                                        name="uscc"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入统一社会信用代码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入统一社会信用代码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业法定代表人姓名"
                                        name="legalName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业法定代表人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业法定代表人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业联系电话"
                                        name="companyNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业联系电话",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业联系电话" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业注册地址"
                                        name="address"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入企业注册地址",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入企业注册地址" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="企业成立日期"
                                        name="incorporationDate"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择企业成立日期",
                                          },
                                        ]}
                                      >
                                        <DatePicker style={{ width: "100%" }} />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex className="message-form-type">
                                  贷款信息
                                </Flex>
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="申请金额(万元)"
                                        name="amount"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入申请金额",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入申请金额" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="期望贷款期限"
                                        name="loanTerm"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择期望贷款期限",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择期望贷款期限"
                                          options={[
                                            { value: "six", label: "6个月" },
                                            { value: "oneYear", label: "1年" },
                                            {
                                              value: "twoYear",
                                              label: "2年",
                                            },
                                            {
                                              value: "threeYear",
                                              label: "3年",
                                            },
                                          ]}
                                          onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="还款方式"
                                        name="repaymentMethod"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请选择还款方式",
                                          },
                                        ]}
                                      >
                                        <Select
                                          // defaultValue=""
                                          style={{ width: "100%" }}
                                          placeholder="请选择还款方式"
                                          options={[
                                            { value: "1", label: "等额本金" },
                                            { value: "2", label: "等额本息" },
                                          ]}
                                          onChange={handleChange}
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  style={{ width: "100%", marginBottom: 20 }}
                                  vertical={true}
                                >
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={4}>
                                      <Form.Item
                                        label="自营贷款类型"
                                        name="selfLoanTypes"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入自营贷款类型",
                                          },
                                        ]}
                                      >
                                        <Radio.Group
                                          value={loanType}
                                          onChange={changeLoanType}
                                          style={{
                                            display: "flex",
                                            flexDirection: "column",
                                            gap: 8,
                                          }}
                                          options={[
                                            {
                                              value: 1,
                                              label:
                                                "固定资产贷款（如厂房、设备购置）",
                                            },
                                            {
                                              value: 2,
                                              label:
                                                "流动资产贷款（如原材料采购、支付账款）",
                                            },
                                          ]}
                                        />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={3}>
                                      <Form.Item
                                        label="贷款用途说明"
                                        name="loanPurpose"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入贷款用途说明",
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          placeholder="例如：用于购买一个新的生产线"
                                          rows={2}
                                          autoSize={{ minRows: 4, maxRows: 4 }}
                                        ></Input.TextArea>
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex
                                  className="message-form-type"
                                  style={{
                                    marginTop: 65,
                                  }}
                                >
                                  联系人信息
                                </Flex>{" "}
                                <Flex style={{ width: "100%" }} vertical={true}>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人姓名"
                                        name="contactName"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人姓名",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人姓名" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人手机号码"
                                        name="contactNumber"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人手机号码",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人手机号码" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                  <Flex
                                    style={{ width: "100%", marginBottom: 20 }}
                                    gap={10}
                                  >
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人电子邮箱"
                                        name="contactEmail"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人电子邮箱",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人电子邮箱" />
                                      </Form.Item>
                                    </Flex>
                                    <Flex flex={1}>
                                      <Form.Item
                                        label="联系人职务"
                                        name="contactPosition"
                                        layout="vertical"
                                        style={{ width: "100%" }}
                                        rules={[
                                          {
                                            required: true,
                                            message: "请输入联系人职务",
                                          },
                                        ]}
                                      >
                                        <Input placeholder="请输入联系人职务" />
                                      </Form.Item>
                                    </Flex>
                                  </Flex>
                                </Flex>
                                <Flex flex={1}>
                                  <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    onClick={submitFormData}
                                  >
                                    提交
                                  </Button>
                                </Flex>
                              </Form>
                            </Flex>
                          </Flex>
                        )}
                        {item.type?.type == "product" && (
                          <Flex className="message-product" wrap>
                            {item.type.productList.length > 0 &&
                              item.type.productList.map(
                                (item: any, index: number) => (
                                  <Flex
                                    key={index}
                                    className="message-product-item"
                                    flex={"30%"}
                                    vertical={true}
                                  >
                                    <Flex className="message-product-item-img">
                                      <img
                                        style={{
                                          width: "100%",
                                          height: "100%",
                                          borderRadius: 5,
                                        }}
                                        src={
                                          index == 0
                                            ? dai
                                            : index == 1
                                            ? dai2
                                            : dai3
                                        }
                                      />
                                    </Flex>
                                    <Flex className="message-product-item-name">
                                      <Flex className="message-product-item-title">
                                        {item.productName}
                                      </Flex>
                                      {/* <Flex flex={1} justify="end">
                                        <Tag
                                          color="blue"
                                          bordered={false}
                                          style={{
                                            marginRight: 0,
                                            fontSize: 12,
                                          }}
                                        >
                                          {item.type}
                                        </Tag>
                                      </Flex> */}
                                    </Flex>
                                    <Flex className="message-product-item-desc">
                                      {item.productDescription}
                                    </Flex>
                                    <Flex
                                      className="message-product-item-card"
                                      gap={10}
                                    >
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        justify="center"
                                        align="center"
                                        flex={1}
                                      >
                                        <Flex>
                                          <AreaChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最高额度
                                        </Flex>
                                        <Flex
                                          className="message-product-card-item-price"
                                          justify="center"
                                          style={{ textAlign: "center" }}
                                        >
                                          {item.coreElements.quota}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <PieChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          最长期限
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.term}
                                        </Flex>
                                      </Flex>
                                      <Flex
                                        className="message-product-card-item"
                                        vertical={true}
                                        flex={1}
                                        justify="center"
                                        align="center"
                                      >
                                        <Flex>
                                          <BarChartOutlined
                                            style={{
                                              fontSize: 16,
                                              color: "#2B7FFE",
                                            }}
                                          />
                                        </Flex>
                                        <Flex className="message-product-card-item-name">
                                          担保方式
                                        </Flex>
                                        <Flex className="message-product-card-item-price">
                                          {item.coreElements.guarantee}
                                        </Flex>
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-hot"
                                      vertical={true}
                                    >
                                      <Flex className="message-product-hot-name">
                                        产品亮点
                                      </Flex>
                                      <Flex
                                        className="message-product-hot-list"
                                        vertical={true}
                                        justify="start"
                                      >
                                        {Array.isArray(
                                          item.productHighlights
                                        ) &&
                                          item.productHighlights.map(
                                            (item: string, index: number) => (
                                              <Flex
                                                key={index}
                                                align="center"
                                                className="message-product-hot-list-item"
                                              >
                                                <Flex>
                                                  <CheckCircleOutlined
                                                    style={{
                                                      background: "#2B7FFE",
                                                      borderRadius: "50%",
                                                      color: "#fff",
                                                      fontSize: 15,
                                                      marginRight: 5,
                                                      height: 15,
                                                    }}
                                                  />
                                                </Flex>
                                                <Flex className="message-product-hot-list-desc">
                                                  {item}
                                                </Flex>
                                              </Flex>
                                            )
                                          )}
                                        {typeof item.productHighlights ==
                                          "string" && (
                                          <Flex
                                            key={index}
                                            align="center"
                                            className="message-product-hot-list-item"
                                          >
                                            <Flex>
                                              <CheckCircleOutlined
                                                style={{
                                                  background: "#2B7FFE",
                                                  borderRadius: "50%",
                                                  color: "#fff",
                                                  fontSize: 15,
                                                  marginRight: 5,
                                                  height: 15,
                                                }}
                                              />
                                            </Flex>
                                            <Flex
                                              className="message-product-hot-list-desc"
                                              flex={1}
                                            >
                                              {item.productHighlights}
                                            </Flex>
                                          </Flex>
                                        )}
                                      </Flex>
                                    </Flex>
                                    <Flex
                                      className="message-product-btn"
                                      flex={1}
                                      justify="end"
                                    >
                                      <Button
                                        type="primary"
                                        size="small"
                                        style={{ marginTop: "auto" }}
                                      >
                                        立即申请
                                      </Button>
                                    </Flex>
                                  </Flex>
                                )
                              )}
                          </Flex>
                        )}
                      </Flex>
                      <Avatar shape="square" icon={<UserOutlined />} />
                    </Flex>
                    <Flex
                      className="message-time"
                      flex={1}
                      justify="end"
                      style={{ paddingRight: 42 }}
                    >
                      {item.time}
                    </Flex>
                    {item.first && (
                      <Flex className="message-btn" style={{ marginLeft: 10 }}>
                        <Space>
                          <Button>人工介入</Button>
                          <Button>在线语音</Button>
                        </Space>
                      </Flex>
                    )}
                  </Flex>
                )}
              </Flex>
            ))}
          </Flex>
        </Flex>
        <Flex vertical={true} className="session-footer">
          <Space size="small" className="session-btn">
            {currentUser == "1" &&
              sessionBtnList.map((item: any) => (
                <div
                  key={item.id}
                  className={
                    item.id == curMessionBtnId
                      ? "session-btn-item session-btn-item-active"
                      : "session-btn-item"
                  }
                  onClick={() => selectSessionBtn(item.id)}
                >
                  {item.name}
                </div>
              ))}
          </Space>
          <Flex flex={1}>
            <Space size="small" className="session-ipt-space">
              <Input.TextArea
                className="session-ipt"
                placeholder="输入内容，按Enter发送，Shift+Enter 换行"
                style={{ fontSize: "14px" }}
                value={msgContent}
                onChange={(e) => setMsgContent(e.target.value)}
                onPressEnter={submitMsg}
              />
              <Button type="primary" onClick={submitMsg}>
                发送
              </Button>
              <Flex
                className={currentUser == "2" ? "upload-btn" : "upload-btn-oth"}
              >
                <Space>
                  <SmileOutlined />
                  <UploadOutlined />
                </Space>
              </Flex>
            </Space>
          </Flex>
        </Flex>
        {showMarketingScript && (
          <Flex className="marketing-script" vertical={true}>
            <Flex align="start" justify="space-between">
              <Flex className="arketing-script-title">AI营销话术建议</Flex>
              <Flex vertical={false} flex={1} justify="end">
                <Button
                  size="small"
                  icon={<RedoOutlined />}
                  style={{ fontSize: 12 }}
                  onClick={sessionBtn}
                >
                  重新生成
                </Button>
                <CloseOutlined
                  style={{ marginLeft: 10 }}
                  onClick={closeSessionScript}
                />
              </Flex>
            </Flex>
            <Flex vertical={true} className="marketing-script-list">
              {marketScript.map((item: string, index: number) => (
                <Flex
                  key={index}
                  className="marketing-script-list-item"
                  onClick={() => {
                    setMsgContent(item);
                    closeSessionScript();
                  }}
                >
                  {item}
                </Flex>
              ))}
            </Flex>
          </Flex>
        )}
      </Flex>
    </>
  );
};
export default memo(Session);
