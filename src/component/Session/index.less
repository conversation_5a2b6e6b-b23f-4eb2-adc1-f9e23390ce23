.session {
  position: relative;
  width: 100%;
}
.session-title {
  padding: 10px 15px;
  font-size: 16px;
}
.session-content {
  width: 100%;
  height: calc(100vh - 315px);
  //   overflow-y: scroll;
  .session-content-message {
    padding: 15px;
    background-color: #f9fafb;
    width: 100%;
    // min-width: 470px;
    height: calc(100vh - 315px);
    overflow-y: scroll;
    overflow-x: hidden;
    .message-item {
      margin-bottom: 30px;
      .ant-avatar {
        min-width: 32px;
      }
      .message-desc {
        margin-left: 10px;
        padding: 10px;
        border: 1px solid #5390f7;
        border-radius: 8px;
        height: max-content;
        font-size: 12px;
        background: #f2f7ff;
        max-width: 750px;
        .sign {
          display: none !important;
        }
      }
      .message-time {
        font-size: 10px;
      }
      .message-business-btn {
        margin-top: 10px;
      }
      .message-upload {
        margin-top: 10px;
        .ant-upload-wrapper {
          width: 100%;
        }
      }
      .message-form {
        margin-top: 10px;
        min-width: 730px;
        .message-form-title {
          margin-bottom: 16px;
          font-size: 16px;
          font-weight: 500;
        }
        .message-form-type {
          margin-bottom: 10px;
          font-size: 14px;
          font-weight: 500;
        }
        .message-form-type-btns {
          margin-bottom: 10px;
          .message-form-btn-item {
            padding: 10px;
            border: 1px solid #e7e9ec;
            border-radius: 4px;
            cursor: pointer;
          }
          .message-form-btn-item-active {
            background: var(--ant-color-primary);
            color: #f9fafb;
          }
        }
      }
      .message-product {
        margin-top: 10px;
        padding: 5px;
        max-width: 730px;
        background-color: #f9fafb;
        border-radius: 8px;
        .message-product-item {
          padding: 8px;
          background: #fff;
          max-width: 239px;
          .message-product-item-img {
            margin-bottom: 12px;
            width: 100%;
            height: 100px;
            border-radius: 5px;
          }
          .message-product-item-name {
            margin-bottom: 8px;
            .message-product-item-title {
              color: #000;
              font-size: 14px;
              font-weight: 500;
            }
          }
          .message-product-item-desc {
            margin-bottom: 8px;
            font-size: 10px;
            color: #00000073;
          }
          .message-product-item-card {
            margin-bottom: 10px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e7e9ec;
            .message-product-card-item {
              padding: 8px;
              background: #f9fafb;
              border-radius: 5px;
              font-size: 10px;

              .message-product-card-item-name {
                margin-top: 5px;
              }
              .message-product-card-item-price {
                text-align: center;
                font-weight: 500;
              }
            }
          }
          .message-product-hot {
            margin-bottom: 10px;
            .message-product-hot-name {
              margin-bottom: 10px;
              font-weight: 500;
            }
            .message-product-hot-list-desc {
              font-size: 10px;
            }
            .message-product-hot-list-item {
              margin-bottom: 5px;
            }
          }
          .message-product-btn {
            margin-bottom: 10px;
          }
        }
      }
    }
    .message-item-right {
      margin-left: auto;
      .message-desc {
        margin-right: 10px;
        background: #3c82f6;
        color: #fff;
      }
      .message-time {
        font-size: 10px;
      }
      .message-business-btn {
        margin-top: 10px;
      }
    }
  }
}
.session-footer {
  height: 110px;
  background: #fff;
  border-top: 1px solid #e7e9ec;
  border-radius: 0 0 10px 10px;
  z-index: 10;
}
.session-btn {
  padding: 10px 10px 0;
  .session-btn-item {
    padding: 4px 6px;
    border: 1px solid #e2e7f0;
    border-radius: 8px;
    font-size: 10px;
    color: #64758b;
    cursor: pointer;
  }
  .session-btn-item-active {
    border-color: #3c82f6;
    background: #f2f7ff;
    color: #3c82f6;
  }
}
.session-ipt-space {
  position: relative;
  display: flex;
  align-items: end;
  padding: 10px;
  width: 100%;
  .upload-btn {
    position: absolute;
    top: 10px;
    right: 100px;
    color: var(--ant-color-primary);
  }
  .upload-btn-oth {
    position: absolute;
    top: -12px;
    right: 100px;
    color: var(--ant-color-primary);
  }
  .ant-space-item {
    &:first-child {
      flex: 1;
      .session-ipt {
        &::placeholder {
          font-size: 12px;
          color: #808080;
        }
      }
    }
  }
}
.marketing-script {
  padding: var(--ant-padding-sm);
  position: absolute;
  left: 5px;
  // bottom: 115px;
  bottom: 0;
  width: calc(100% - 10px);
  height: 155px;
  background: var(--ant-color-white);
  border: 1px solid #e7e9ec;
  border-radius: var(--ant-border-radius);
  .marketing-script-list {
    margin-top: 12px;
    overflow-y: scroll;
    height: calc(100% - 35px);
    .marketing-script-list-item {
      padding: 10px;
      background: #f1f3f7;
      border-radius: 5px;
      margin-bottom: 12px;
      font-size: 12px;
      cursor: pointer;
    }
  }
  .arketing-script-title {
    font-size: 14px;
  }
}
.move-up-div {
  animation: moveUp 1s forwards;
}
@keyframes moveUp {
  from {
    bottom: 0; /* 从底部开始 */
    position: absolute; /* 定位方式 */
  }
  to {
    bottom: 115px; /* 移动到顶部 */
  }
}
.move-down-div {
  animation: moveDown 1s forwards;
}
@keyframes moveDown {
  from {
    bottom: 115px;
    position: absolute;
  }
  to {
    bottom: 0;
  }
}
.ant-upload-drag {
  border: 1px dashed #e0eaff;
  border-radius: 12px;
  padding: 40px 0px;
  background: #ffffff;
}
.ant-upload-drag-icon {
  margin-bottom: 0;
  font-size: 36px;
  color: #1890ff;
  transition: transform 0.3s ease;
}
.ant-upload-text,
.ant-upload-hint {
  font-size: 14px;
  span:nth-child(1) {
    display: block;
    font-family: Alibaba PuHuiTi 3;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
  }
  span:nth-child(2) {
    display: block;
    opacity: 0.6;
    font-family: Alibaba PuHuiTi 3;
    font-size: 12px;
    font-weight: normal;
    margin-top: 5px;
  }
}
